<template>
  <div class="box-container" style="overflow-x: hidden;" v-loading="loading">
    <!--系统基本信息-->
    <el-form ref="form" :model="form" :rules="rules" >
      <el-row>
        <el-col :span="24">
          <!-- 动态渲染字段 -->
          <template v-for="group in basicInfoAssetFields">
            <el-row :gutter="20" type="flex" :key="group.formName" style="flex-wrap: wrap;margin-bottom: 10px;" label-position="top">
              <el-col :span="24">
                <div class="my-title" v-if="group.isShow">
                  <img v-if="group.formName === '基本信息'" src="@/assets/images/application/jbxx.png" alt=""/>
                  <img v-else-if="group.formName === '备案信息'" src="@/assets/images/application/baxx.png" alt=""/>
                  <img v-else-if="group.formName === '测评信息'" src="@/assets/images/application/cpxx.png" alt=""/>
                  <img v-else-if="group.formName === '外部连接信息'" src="@/assets/images/application/wblj.png" alt=""/>
                  <img v-else-if="group.formName === '拓扑结构信息'" src="@/assets/images/application/tpxx.png" alt=""/>
                  <img v-else-if="group.formName === '运营维护情况'" src="@/assets/images/application/ywxx.png" alt=""/>
                  <img v-else-if="group.formName === '其他基本信息'" src="@/assets/images/application/qtxx.png" alt=""/>
                  {{ group.formName }}
                </div>
              </el-col>
              <div class="my-form" v-if="group.isShow">
                <template v-if="group.formName === '外部连接信息'">
                  <el-col :span="24">
                    <ApplicationLink
                      v-if="group.isShow"
                      :fields="group.fieldsItems"
                      :disabled="!$editable.value"
                      v-model="form.links"/>
                  </el-col>
                </template>

                <template v-else-if="group.formName === '运营维护情况'">
                  <el-col :span="24">
                    <ApplicationSite
                      ref="site"
                      v-if="group.isShow"
                      :fields="group.fieldsItems"
                      :disabled="!$editable.value"
                      :value.sync="form.eids"
                      :asset-id="form.assetId"
                      multiple/>
                  </el-col>
                </template>

                <template v-else>
                  <el-col
                    v-for="field in group.fieldsItems"
                    :key="field.fieldKey"
                    :style="radioGroupType.includes(field.fieldKey) ? { display: 'flex' } : ''"
                    :span="getFieldSpan(field)"
                  >
                    <!-- 其他系统备注 -->
                    <template v-if="field.fieldKey === 'otherSystemNotes'">
                      <el-form-item
                        :label="field.fieldName"
                        :prop="field.fieldKey"
                        :rules="getFieldRules(field)"
                        v-if="form.systemType == 12"
                      >
                        <el-input
                          v-model="form.otherSystemNotes"
                          placeholder="请输入其它系统备注"
                        />
                      </el-form-item>
                    </template>
                    <!-- 其他基本信息：信创适配时间 -->
                    <template v-else-if="field.fieldKey === 'adaptDate'">
                      <el-form-item
                        :label="field.fieldName"
                        :prop="field.fieldKey"
                        :rules="getFieldRules(field)"
                        v-if="form.isadapt === 'Y'"
                      >
                        <el-date-picker
                          clearable
                          v-model="form.adaptDate"
                          type="date"
                          format="yyyy 年 MM 月 dd 日"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择建设时间">
                        </el-date-picker>
                      </el-form-item>
                    </template>
                    <!-- 其他基本信息：密码应用建设时间 -->
                    <template v-else-if="field.fieldKey === 'cipherDate'">
                      <el-form-item
                        :label="field.fieldName"
                        :prop="field.fieldKey"
                        :rules="getFieldRules(field)"
                        v-if="form.iscipher === 'Y'"
                      >
                        <el-date-picker
                          clearable
                          v-model="form.cipherDate"
                          type="date"
                          format="yyyy 年 MM 月 dd 日"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择建设时间">
                        </el-date-picker>
                      </el-form-item>
                    </template>
                    <!-- 其他基本信息：警综对接 -->
                    <template v-else-if="field.fieldKey === 'islink'">
                      <el-form-item
                        :label="field.fieldName"
                        :prop="field.fieldKey"
                        :rules="getFieldRules(field)"
                        v-if="deployLocation === 'fair'"
                      >
                        <el-radio-group v-model="form.islink">
                          <el-radio
                            v-for="dict in dict.type.sys_yes_no"
                            :key="dict.value"
                            :label="dict.value"
                          >{{ dict.label }}
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </template>
                    <!-- 关联服务器字段处理 -->
                    <template v-else-if="field.fieldKey === 'associationServer'">
                      <el-form-item
                        :label="field.fieldName"
                        :prop="field.fieldKey"
                        :rules="getFieldRules(field)"
                        class="associationServer"
                      >
                        <el-select
                          v-model="form.associationServer"
                          placeholder="请选择服务器"
                          multiple
                          filterable
                          clearable
                          @change="serverChange"
                        >
                          <el-option
                            v-for="server in serverOptions"
                            :key="server.assetId"
                            :label="server.assetName"
                            :value="server.assetId"
                          >
                            <span style="float: left">{{ server.assetName }}</span>
                            <span style="float: right; color: #8492a6; font-size: 13px">{{ server.ip }}</span>
                          </el-option>
                        </el-select>
                        <el-button
                          size="mini"
                          plain
                          type="primary"
                          style="margin-left: 10px;"
                          @click="addAssetHandle"
                          :disabled="!$editable.value"
                        >
                          新增服务器
                        </el-button>
                      </el-form-item>
                    </template>

                    <el-form-item
                      v-else
                      :label="field.fieldName"
                      :prop="field.fieldKey"
                      :rules="getFieldRules(field)"
                    >
                      <template v-if="field.fieldKey === 'deptId'">
                        <dept-select
                          v-model="form.deptId"
                          is-current
                          :isAllData="!$editable.value"
                        />
                      </template>

                      <template v-else-if="field.fieldKey === 'manager'">
                        <user-select
                          v-model="form.manager"
                          :placeholder="managePlaceholder"
                          :userdata="userdata"
                          multiple
                          @setPhone="handleUserSelect"
                        />
                      </template>

                      <template v-else-if="field.fieldKey === 'domainId'">
                        <NetworkSelect v-model="form.domainId"/>
                      </template>

                      <template v-else-if="field.fieldKey === 'tags'">
                        <Dynamic-Tag v-model="form.tags"/>
                      </template>

                      <template v-else-if="field.fieldKey === 'vendor'">
                        <VendorSelect2
                          v-model="form.vendors"
                          multiple
                          placeholder="请选择开发合作企业"
                          :vendorsdata="vendorsdata"
                          :selectVendor="selectVendor"
                          :isDisabled="!$editable.value"/>
                      </template>

                      <template v-else-if="field.fieldKey === 'waitingInsuranceFilingScan' || field.fieldKey === 'evaluationReport' || field.fieldKey === 'netTopo'">
                        <file-upload
                          v-model="form[field.fieldKey]"
                          :limit="5"
                          :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                        />
                      </template>

                      <!-- 默认字段渲染 -->
                      <template v-else>
                        <!-- 下拉选择框 -->
                        <el-select
                          v-if="selectType.includes(field.fieldKey)"
                          v-model="form[field.fieldKey]"
                          :placeholder="field.placeholder || `请选择${field.fieldName}`"
                          :popper-append-to-body="false"
                          clearable
                          filterable
                          v-bind="field.props"
                        >
                          <el-option
                            v-for="item in getDictOptions(field.fieldKey)"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>

                        <!-- 日期选择器 -->
                        <el-date-picker
                          v-else-if="dateType.includes(field.fieldKey)"
                          v-model="form[field.fieldKey]"
                          :type="getDateType(field)"
                          :placeholder="field.placeholder || `请选择${field.fieldName}`"
                          v-bind="field.props"
                        />

                        <!-- 单选按钮组 -->
                        <el-radio-group
                          v-else-if="radioGroupType.includes(field.fieldKey)"
                          v-model="form[field.fieldKey]"
                          v-bind="field.props"
                        >
                          <el-radio
                            v-for="item in dict.type.sys_yes_no"
                            :key="item.value"
                            :label="item.value"
                            @change="field.fieldKey === 'isadapt' ? showDapt : field.fieldKey === 'iscipher' ? showCipher : null "
                          >{{ item.label }}</el-radio>
                        </el-radio-group>

                        <!-- 多行文本输入 -->
                        <el-input
                          v-else-if="textareaType.includes(field.fieldKey)"
                          v-model="form[field.fieldKey]"
                          type="textarea"
                          :rows="3"
                          :placeholder="field.placeholder || `请输入${field.fieldName}`"
                          v-bind="field.props"
                        />

                        <!-- 文件上传 -->
                        <file-upload
                          v-else-if="field.type === 'file'"
                          v-model="form[field.fieldKey]"
                          :limit="5"
                          :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                          v-bind="field.props"
                        />

                        <!-- 默认文本输入 -->
                        <el-input
                          v-else
                          v-model="form[field.fieldKey]"
                          :placeholder="field.placeholder || `请输入${field.fieldName}`"
                          v-bind="field.props"
                        />
                      </template>
                    </el-form-item>
                  </el-col>
                </template>
              </div>
            </el-row>
          </template>
        </el-col>
      </el-row>
    </el-form>

    <!--业务基本信息-->
    <el-form ref="businessForm" :model="businessForm" :rules="businessRules">
      <el-row>
        <el-col :span="24">
          <!-- 动态渲染业务信息字段 -->
          <template v-for="group in businessAssetFields">
            <el-row :key="group.formName" :gutter="20" type="flex" style="flex-wrap: wrap;margin: 20px 0;">
              <el-col :span="24">
                <div class="my-title">
                  <img v-if="group.formName === '用户规模' && group.isShow" src="@/assets/images/application/yhgm.png" alt=""/>
                  <img v-else-if="group.formName === '业务描述' && group.isShow" src="@/assets/images/application/ywms.png" alt=""/>
                  <img v-else-if="group.formName === '功能模块' && group.isShow" src="@/assets/images/application/gnmk.png" alt=""/>
                  {{ group.formName }}
                </div>
              </el-col>
              <div class="my-form" v-if="group.isShow">
                <!-- 用户规模特殊处理 -->
                <template v-if="group.formName === '用户规模'">
                  <el-col :span="8">
                    <el-form-item label="覆盖地域" prop="coverArea">
                      <el-select v-model="businessForm.coverArea" placeholder="请选择覆盖地域">
                        <el-option
                          v-for="dict in dict.type.cover_area"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="使用对象" prop="serviceGroup">
                      <dict-select v-model="businessForm.serviceGroup" dict-name="serve_group" placeholder="请选择使用对象"
                                   multiple
                                   clearable/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="授权用户数" prop="userNums">
                      <el-input
                        type="number"
                        min="0"
                        v-model="businessForm.userNums" @input="val=>inputToString(val,'userNums')"
                        placeholder="输入用户数"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="系统日均访客量" prop="everydayVisitNums">
                      <el-input
                        type="number"
                        min="0"
                        v-model="businessForm.everydayVisitNums"
                        @input="val=>inputToString(val,'everydayVisitNums')"
                        placeholder="输入系统日均访客量"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="系统月均活跃人数" prop="everydayActiveNums">
                      <el-input
                        type="number"
                        min="0"
                        v-model="businessForm.everydayActiveNums"
                        @input="val=>inputToString(val,'everydayActiveNums')"
                        placeholder="输入系统月均活跃人数"
                      />
                    </el-form-item>
                  </el-col>
                </template>

                <!-- 业务描述特殊处理 -->
                <template v-else-if="group.formName === '业务描述'">
                  <el-col :span="24">
                    <el-form-item :label="'总体系统业务说明'" prop="sysBusinessState">
                      <el-input :rows="6" :maxlength="800" v-model="businessForm.sysBusinessState" type="textarea"
                                placeholder="请输入系统业务说明.."/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="上传操作手册" prop="operateHandbook">
                      <file-upload :disUpload="!$editable.value"
                                   v-model="businessForm.operateHandbook"
                                   :limit="5"
                                   :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                      />
                    </el-form-item>
                  </el-col>
                </template>

                <!-- 功能模块特殊处理 -->
                <template v-else-if="group.formName === '功能模块'">
                  <el-col :span="24" class="my-form">
                    <div v-for="(item, index) in functionStateList" :key="index">
                      <el-form ref="moduleForm" :model="item" :rules="moduleRule" :disabled="!$editable.value">
                        <el-row :gutter="20" type="flex" align="middle" style="flex-wrap: wrap;">
                          <el-col :span="4">
                            <el-form-item prop="moduleName">
                              <el-input v-model="item.moduleName" placeholder="功能模块名称"/>
                            </el-form-item>
                          </el-col>
                          <el-col :span="18">
                            <el-form-item prop="moduleDesc">
                              <el-input :rows="3" v-model="item.moduleDesc" type="textarea"
                                        placeholder="请将该功能模块实现的功能、用途做全面的说明，并保证内容应正确、完整、一致和可验证。"/>
                            </el-form-item>
                          </el-col>
                          <el-col :span="2">
                            <el-button
                              size="mini"
                              type="text"
                              icon="el-icon-remove"
                              @click="handleDel(item.moduleId,index)"
                            >删除
                            </el-button>
                          </el-col>
                        </el-row>
                      </el-form>
                    </div>
                    <el-row type="flex" justify="end" style="flex-wrap: wrap;">
                      <el-col :span="2">
                        <el-button
                          type="primary"
                          plain
                          icon="el-icon-plus"
                          size="mini"
                          @click="handleAdd"
                        >新增
                        </el-button>
                      </el-col>
                    </el-row>
                  </el-col>
                </template>
              </div>
            </el-row>
          </template>
        </el-col>
      </el-row>
    </el-form>

    <!--系统软硬件环境-->
    <el-form ref="hardWareForm">
      <el-row>
        <el-col :span="24">
          <!-- 动态渲染软硬件环境字段 -->
          <template v-for="group in runTimeAssetFields">
            <el-row :key="group.formName" type="flex" style="flex-wrap: wrap;margin: 20px 0;">
              <el-col :span="24">
                <div class="my-title">
                  <img v-if="group.formName === '所安装服务器环境'" src="@/assets/images/application/fwq.png" alt=""/>
                  <img v-else-if="group.formName === '所安装数据库环境'" src="@/assets/images/application/sjk.png" alt=""/>
                  <img v-else-if="group.formName === '关联网络设备'" src="@/assets/images/application/wlsb.png" alt=""/>
                  <img v-else-if="group.formName === '关联安全设备'" src="@/assets/images/application/aqsb.png" alt=""/>
                  {{ group.formName }}
                </div>
              </el-col>
              <el-col :span="24">
                <!-- 根据分组名称渲染不同子组件 -->
                <template v-if="group.formName === '所安装服务器环境'">
                  <serverEV
                    ref="serverEV"
                    :function-list.sync="functionStateList"
                    :asset-id="assetId"
                    :data-list="currentAssociationServer"
                    @selected="serverSelect"
                    v-if="afterInit"
                    :fields="group.fieldsItems"
                  />
                </template>

                <template v-else-if="group.formName === '所安装数据库环境'">
                  <dateEV
                    ref="dateEV"
                    :function-list.sync="functionStateList"
                    :asset-id="assetId"
                    :fields="group.fieldsItems"
                  />
                </template>

                <template v-else-if="group.formName === '关联网络设备'">
                  <networkEV
                    ref="networkEV"
                    :asset-id="assetId"
                    :fields="group.fieldsItems"
                  />
                </template>

                <template v-else-if="group.formName === '关联安全设备'">
                  <safeEV
                    ref="safeEV"
                    :asset-id="assetId"
                    :fields="group.fieldsItems"/>
                </template>
              </el-col>
            </el-row>
          </template>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog title="添加服务器" width="900" append-to-body :visible.sync="showAddServer">
      <edit-server v-if="showAddServer" ref="adds" @cancel="addServerCancel" @confirm="addServerSuccess"></edit-server>
    </el-dialog>
  </div>
</template>

<script>
import {addApplicationInfo, updateApplicationInfo, getApplication} from "@/api/safe/application";
import ApplicationLink from '@/views/hhlCode/component/application/applicationLink';
import ApplicationSite from '@/views/hhlCode/component/application/applicationSite';
import UserSelect from '@/views/hhlCode/component/userSelect';
import DeptSelect from '@/views/components/select/deptSelect';
import NetworkSelect from '@/views/components/select/networkSelect';
import DynamicTag from '@/components/DynamicTag';
import VendorSelect2 from '@/views/components/select/vendorSelect2';
import DictSelect from '@/views/components/select/dictSelect';
import {getValFromObject} from "@/utils";
import {generateSecureUUID, waitForValue} from "@/utils/ruoyi";
import {listVendorByApplication} from "@/api/safe/vendor";
import serverEV from "@/views/hhlCode/component/application/applicationHardware/serverEV.vue";
import dateEV from "@/views/hhlCode/component/application/applicationHardware/dateEV.vue";
import networkEV from "@/views/hhlCode/component/application/applicationHardware/networkEV.vue";
import safeEV from "@/views/hhlCode/component/application/applicationHardware/safeEV.vue";
import overViewSelect from "@/views/components/select/overViewSelect.vue";
import {listAllOverview} from "@/api/safe/overview";
import EditServer from "@/views/safe/server/editServer.vue";

export default {
  name: "systemDetails",
  components: {
    EditServer,
    overViewSelect,
    safeEV,
    networkEV,
    dateEV,
    serverEV,
    ApplicationLink,
    ApplicationSite,
    UserSelect,
    DeptSelect,
    NetworkSelect,
    DictSelect,
    DynamicTag,
    VendorSelect2,
  },
  dicts: [
    'serve_group',
    'cover_area',
    'sys_yes_no',
    'app_net_scale',
    'construct_type',
    'system_type',
    'protection_grade',
    'asset_state',
    'app_login_type',
    'app_technical',
    'app_deploy',
    'app_storage',
    'evaluation_results',
    'evaluation_status'
  ],
  inject: {
    $editable: {
      default: {value: true},
    }
  },
  props: {
    assetId: {
      type: [String, Number],
      required: false,
      default: null,
    },
    changeId: Function,
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    assetList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      loading: false,
      collapseNames: ['1', '2', '3', '4', '5'],
      vendorsdata: '1',
      userdata: '1',
      functionStateList: [{}, {}, {}],
      // 基本信息表单参数
      form: {},
      // 业务信息表单参数
      businessForm: {
        delList: []
      },
      // 表单校验
      rules: {
        assetName: [
          {required: true, message: "应用名称不能为空", trigger: "blur"},
          {min: 0, max: 64, message: '应用名称不能超过 64 个字符', trigger: 'blur'},
        ],
        assetClass: [
          {required: true, message: "资产分类不能为空", trigger: "blur"},
        ],
        domainId : [
          {required: true, message: "主部署网络不能为空", trigger: "blur"},
        ],
        domainUrl: [
          {min: 0, max: 128, message: '域名不能超过 128 个字符', trigger: 'blur'},
          {
            pattern: /^(?=^.{3,255}$)(http(s)?:\/\/)?(www\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\d+)*(\/\w+\.\w+)*$/,
            message: "请输入正确的域名",
            trigger: ['blur', 'change']
          },
        ],
        manager: [
          {required: true, message: "负责人不能为空", trigger: "blur"},
        ],
        userId: [
          // { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        deptId: [
          {required: true, message: "单位不能为空", trigger: "blur"},
        ],
        phone: [
          {min: 0, max: 11, message: '联系电话不能超过 11 位', trigger: 'blur'},
          {pattern: /^1[1|2|3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的联系电话", trigger: ['blur', 'change']},
        ],
        url: [
          {required: true, message: "登录地址不能为空", trigger: "blur"},
          {min: 0, max: 128, message: '登录地址不能超过 128 个字符', trigger: 'blur'},
          {
            // 正则表达式用于验证 URL 格式
            // 支持 http/https 协议，允许 IP 地址或域名，支持端口号和路径
            //pattern: /^(https?:\/\/)?(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}|((\d{1,3}\.){3}\d{1,3}))(:\d+)?(\/[\w.-]*)*$/,
            //pattern: /^(?:#\/?[^\s#]+|(https?|ftp):\/\/([\w.-]+|\[[\da-fA-F:]+\])(:\d+)?(\/[^?\s#]*)?(\?[^\s#]*)?(#.*)?)$/,
            pattern: /^(https?:\/\/)?((([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})|((\d{1,3}\.){3}\d{1,3}))(:\d+)?(\/[^\s?#]*)?(\?[^\s#]*)?(#.*)?$/,
            message: "请输入正确的登录地址",
            trigger: ['blur', 'change']
          }
        ],
        ipd: [
          {required: true, message: "Ip地址段不能为空", trigger: "blur"},
          {min: 0, max: 320, message: 'IP地址段填写已上限', trigger: 'blur'},
          {
            pattern: /^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}(,((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3})*$/,
            message: "请输入正确的IP地址段，多个IP地址用逗号隔开",
            trigger: ['blur', 'change']
          },
        ],

        netMemo: [
          {min: 0, max: 255, message: '拓扑图说明不能超过 255 个字符', trigger: 'blur'},
        ],
      },
      businessRules: {
        sysBusinessState: [
          {required: false, min: 0, max: 800, message: '拓扑图说明不能超过 800 个字符', trigger: 'blur'},
        ],
        userNums: [
          {required: false, max: 12, message: '用户数量不能超过 12 个字符', trigger: 'blur'},
          {required: false, pattern: /^[0-9]*$/, message: '请输入大于等于0的数字', trigger: 'blur'},
        ],
        everydayVisitNums: [
          {min: 0, max: 12, message: '日均访问数量不能超过 12 个字符'},
          {required: false, pattern: /^[0-9]*?$/, message: '请输入大于等于0的数字', trigger: 'blur'},
        ],
        everydayActiveNums: [
          {min: 0, max: 12, message: '月均活跃人数不能超过 12 个字符'},
          {required: false, pattern: /^[0-9]*?$/, message: '请输入大于等于0的数字', trigger: 'blur'},
        ],
      },
      moduleRule: {
        moduleName: [
          {min: 0, max: 64, message: '功能模块名称不能超过 64 个字符', trigger: 'blur'},
        ],
        moduleDesc: [
          {min: 0, max: 2000, message: '功能模块说明不能超过 2000 个字符', trigger: 'blur'},
        ],
      },
      gv: getValFromObject,
      deployLocation: localStorage.getItem("dl"),
      managerLabel: '责任人/电话',
      managePlaceholder: '请选择责任人',
      refs: {
        'networkEV': "所安装服务器环境",
        'safeEV': '所安装数据环境',
        'serverEV': '关联网络设备',
        'dateEV': "关联安全设备"
      },
      collapse: ['1', '2', '3', '4'],
      showAddServer: false,
      serverOptions: [],
      currentAssociationServer: [],
      afterInit: false,
      selectType: ['systemType','construct','loginType','technical','deploy','state','protectGrade', 'evaluationResults', 'evaluationStatus'],
      dateType: ['uodTime', 'waitingInsuranceFilingTime', 'evaluationYear'],
      textareaType: ['netMemo'],
      radioGroupType: ['iskey', 'isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink'],
    }
  },
  mounted() {
    this.getAllServerList();
    this.$nextTick(() => {
      if (this.deployLocation === 'fair') {
        this.managerLabel = '责任民警/电话'
        this.managePlaceholder = '请选择责任民警'
      }
      this.reset();
      this.init()
    });
  },
  activated() {
    this.$nextTick(() => {
      this.reset();
      this.init()
    });
  },
  watch: {
    functionStateList: {
      handler(newVal, oldVal) {
        if (newVal && newVal.length > 0) {
          newVal.forEach((item, index) => {
            if(Object.keys(item).length > 0){
              item.tempId = generateSecureUUID();
            }
          })
        }
      },
    }
  },
  computed: {
    // 业务信息动态字段
    businessAssetFields() {
      return (this.assetList.slice(7, 10) || []).map(group => ({
        ...group,
        fieldsItems: group.fieldsItems.filter(item => item.isShow)
      }));
    },
    // 基本信息动态字段
    basicInfoAssetFields() {
      let assetFields = this.assetList.slice(0, 7);
      return assetFields.map(group => {
        return {
          ...group,
          fieldsItems: group.fieldsItems.filter(item => item.isShow)
        };
      })
    },
    // 软硬件环境动态字段
    runTimeAssetFields() {
      return (this.assetList.slice(10, 14) || []).map(group => ({
        ...group,
        fieldsItems: group.fieldsItems.filter(item => item.isShow)
      }));
    },
  },
  methods: {
    // 获取字段校验规则
    getFieldRules(field) {
      // 1. 优先使用字段配置中的规则
      if (field.rules && field.rules.length > 0) {
        return field.rules;
      }

      // 2. 使用组件中预定义的规则（同时检查 rules 和 businessRules）
      const ruleSets = [this.rules, this.businessRules];
      for (const ruleSet of ruleSets) {
        if (ruleSet[field.fieldKey]) {
          return ruleSet[field.fieldKey];
        }
      }

      // 3. 动态生成基础规则
      const baseRules = [];

      // 根据字段类型添加默认规则
      if (field.required) {
        baseRules.push({
          required: true,
          message: `${field.fieldName}不能为空`,
          trigger: ['blur', 'change']
        });
      }

      // 添加长度限制规则
      if (field.maxLength) {
        baseRules.push({
          max: field.maxLength,
          message: `${field.fieldName}不能超过 ${field.maxLength} 个字符`,
          trigger: 'blur'
        });
      }

      // 添加正则校验规则
      if (field.pattern) {
        baseRules.push({
          pattern: new RegExp(field.pattern),
          message: field.patternMessage || `请输入正确的${field.fieldName}格式`,
          trigger: ['blur', 'change']
        });
      }

      return baseRules.length > 0 ? baseRules : undefined;
    },

    // 获取字段所占列数
    getFieldSpan(field) {
      // 特殊字段占24列
      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan'];
      if (fullSpanFields.includes(field.fieldKey)) return 24;
      // 其他字段默认占8列
      return 8;
    },

    getDictOptions(fieldKey) {
      const dictMap = {
        systemType: 'system_type',
        construct: 'construct_type',
        loginType: 'app_login_type',
        technical: 'app_technical',
        deploy: 'app_deploy',
        state: 'asset_state',
        protectGrade: 'protection_grade',
        evaluationResults: 'evaluation_results',
        evaluationStatus: 'evaluation_status',
      };

      return this.dict.type[dictMap[fieldKey]] || [];
    },

    // 获取字段的日期类型
    getDateType(field) {
      switch (field.fieldKey) {
        case 'uodTime':
          return 'date';
          case 'evaluationYear':
            return 'year';
        default:
          return 'date';
      }
    },

    //信创适配时间显示
    showDapt() {
      if (this.form.isadapt === 'N') {
        this.form.adaptDate = null;
      }
    },
    //密屏应用建设时间显示
    showCipher() {
      if (this.form.iscipher === 'N') {
        this.form.cipherDate = null;
      }
    },

    selectVendor(params) {
      if (this.form.vendors == null || this.form.vendors == '') {
        this.vendorsdata = null;
      } else {
        this.vendorsdata = '1';
      }
      return listVendorByApplication({
        applicationId: this.assetId,
        applicationCode: this.form.vendors,
        ...params
      });
    },
    getAllServerList(){
      listAllOverview({"assetClass":4}).then(res =>{
        this.serverOptions = res.data;
      })
    },
    /** 初始化 */
    async init() {
      // let params = this.$route.query;
      if (this.assetId) {
        await getApplication(this.assetId).then(response => {
          // 获取应用信息详情
          this.form.assetId = this.assetId;
          this.form = response.data.applicationVO;
          this.form.systemType = response.data.applicationVO.systemType.toString();
          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {
            site.getList()
          })
          // 获取业务信息详情
          this.businessForm.assetId = this.assetId;
          this.businessForm = response.data.tblBusinessApplication;
          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';
          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';
          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';
          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];
          if (this.functionStateList.length < 3) {
            let i = 0;
            while (i < 3 - this.functionStateList.length) {
              this.functionStateList.push({});
            }
          }
        }).finally(()=>{
          this.afterInit = true;
        })
      }else {
        this.afterInit = true;
      }
    },

    // 校验数据
    validateForm() {
      let flag = true;
      this.$refs['form'].validate(valid => {
        if (!valid) {
          flag = false;
        }
      });
      this.$refs['businessForm'].validate(valid => {
        if (!valid) {
          flag = false;
        }
      });
      return flag;
    },

    /** 保存按钮操作 */
    handleSave() {
      return new Promise((resolve, reject) => {
        let pass1, pass2 = true;
        this.$refs["form"].validate(valid => pass1 = valid); // 系统基本信息校验
        this.$refs["businessForm"].validate(valid => pass2 = valid); // 业务信息校验

        // 处理系统基本信息
        let link = this.filterNull(this.form.links)
        link.forEach(l => {
          if (!(l.linkIp && l.linkIp.length > 0)) {
            l.linkIp = null;
          }
          if (!(l.linkPort && l.linkPort.length > 0)) {
            l.linkPort = null;
          }
        })
        this.form.links = link;

        // 处理业务信息
        this.businessForm.tblMapperList = this.functionStateList.filter(fun => fun.moduleName);
        for (let moduleFormKey in this.$refs.moduleForm) {
          if (!pass2) return reject();
          this.$refs.moduleForm[moduleFormKey].validate(valid => pass2 = valid)
        }

        console.log(this.$refs.networkEV, this.$refs.safeEV, this.$refs.serverEV, this.$refs.dateEV, 'sssssss')

        // 处理软硬件信息
        let form = {};
        const hardwareComponents = [
          'serverEV',
          'dateEV',
          'networkEV',
          'safeEV'
        ];

        for (let ref of hardwareComponents) {
          const component = this.$refs[ref];
          if (!component) {
            console.error(`${ref} 组件未找到`);
            continue;
          }

          const compInstance = Array.isArray(component) ? component[0] : component;

          try {
            const data = compInstance.submit();
            if (typeof data === 'string') {
              this.$message.error(data);
              return;
            }
            form[ref] = { list: data.list, delList: data.delList };
          } catch (error) {
            console.error(`调用 ${ref}.submit() 失败:`, error);
            this.$message.error(`${ref} 提交失败: ${error.message}`);
          }
        }


        // 整合参数
        let params = {
          serialVersionUID: 0,
          applicationVO: this.form,
          tblBusinessApplication: this.businessForm,
          hardWareEV: form,
        }

        if (this.assetId != null) {
          updateApplicationInfo(params).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.init()
            return resolve();
          }).catch((err) => {
            return reject(err);
          });
        } else {
          addApplicationInfo(params).then(response => {
            this.form.assetId = response.data;
            this.changeId(response.data);
            this.$modal.msgSuccess("新增成功");
            this.init()
            return resolve();
          }).catch(err => {
            return reject(err);
          });
        }
      })
    },

    inputToString(val, name) {
      if (val)
        this.form[name] = "" + val;
    },

    handleAdd() {
      this.functionStateList.push({});
      this.functionStateList.forEach((item, index) => {
        item.tempId = generateSecureUUID();
      })
    },
    handleDel(moduleId, index) {
      console.log('del',this.functionStateList)
      this.functionStateList.splice(index, 1);
      if (!this.businessForm.delList) {
        this.businessForm.delList = [];
      }
      this.businessForm.delList.push(moduleId);
    },

    /** 表单重置 */
    reset() {
      this.form = {
        assetId: undefined,
        assetCode: undefined,
        assetName: undefined,
        softwareVersion: undefined,
        degreeImportance: undefined,
        manager: undefined,
        domainUrl: undefined,
        systemType: undefined,
        phone: undefined,
        assetType: undefined,
        assetTypeDesc: undefined,
        assetClass: undefined,
        assetClassDesc: undefined,
        construct: undefined,
        netType: undefined,
        appType: undefined,
        serviceGroup: undefined,
        frequency: undefined,
        usageCount: undefined,
        userScale: undefined,
        userObject: undefined,
        url: undefined,
        ipd: undefined,
        technical: undefined,
        deploy: undefined,
        storage: undefined,
        netenv: undefined,
        iskey: undefined,
        datanum: undefined,
        isbase: "0",
        islink: undefined,
        ishare: undefined,
        islog: undefined,
        isplan: undefined,
        isadapt: undefined,
        iscipher: undefined,
        adaptDate: undefined,
        cipherDate: undefined,
        function: undefined,
        remark: undefined,
        userId: undefined,
        deptId: undefined,
        orgnId: undefined,
        vendors: undefined,
        upTime: undefined,
        dwid: undefined,
        contactor: undefined,
        domainId: undefined,
        netScale: undefined,
        netTopo: undefined,
        netMemo: undefined,
        tags: "",
        links: [],
        eids: [],
      };
      this.businessForm = {
        sysBusinessState: undefined,
        userNums: undefined,
        everydayVisitNums: undefined,
        everydayActiveNums: undefined,
      };
      this.resetForm("form");
      this.resetForm("businessForm");
    },
    /** 用户选择 */
    handleUserSelect(val) {
      if (this.form.manager == null || this.form.manager == '') {
        this.userdata = null;
      } else {
        this.userdata = '1';
      }
      this.form.phone = val;
    },
    /** 过滤空值 */
    filterNull(value) {
      // return value.filter((item) => JSON.stringify(item) !== '{}');
      return value.filter((item) => Object.keys(item).length !== 0);
    },
    addServerSuccess(row){
      this.getAllServerList();
      this.showAddServer = false;
    },
    addServerCancel(){
      this.showAddServer = false;
    },
    addAssetHandle(){
      this.showAddServer = true;
    },
    serverSelect(data){
      if(data){
        this.$set(this.form, 'associationServer', data.map(item => item.serverId))
      }
    },
    serverChange(val){
      if(!val || val.length<1){
        this.currentAssociationServer = [];
      }else {
        this.currentAssociationServer = val.map(item => this.serverOptions.find(server => server.assetId === item));
      }
      return val;
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/customForm";
.box-container {
  padding-right: 20px;
  .my-form {
    width: 100%;
    margin: 0 10px;
  }
}
::v-deep .el-select-dropdown {
  position: absolute !important;
  left: 0 !important;
  top: 30px!important;
}
::v-deep .el-date-editor {
  width: 100%;
}

::v-deep .associationServer{
  .el-form-item__content{
    display: flex;
    align-items: center;
  }
  .el-form-item__label{
    display: contents;
  }
}
</style>
