<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-position="right"
          label-width="70px"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="最近告警时间" label-width="98px">
                <el-date-picker
                  v-model="rangeTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="资产IP">
                <el-input
                  v-model="queryParams.victimIp"
                  placeholder="请输入资产IP"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
<!--            <el-col :span="6">
              <el-form-item label="告警等级" prop="alarmLevel">
                <el-select
                  v-model="queryParams.alarmLevel"
                  placeholder="请选择告警等级"
                >
                  <el-option
                    v-for="dict in dict.type.threaten_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>-->
            <el-col :span="6">
              <el-form-item label="所属部门" prop="deptId">
                <dept-select v-model="queryParams.deptId" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleQuery"
                >查询</el-button>
                <el-button class="btn2" size="small" @click="resetQuery"
                >重置</el-button>
<!--                <el-button class="btn2" size="small" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll">
                  展开
                </el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-up" @click="showAll=false" v-else>收起
                </el-button>-->
              </el-form-item>
            </el-col>
          </el-row>
<!--          <el-row v-if="showAll" :gutter="10">

          </el-row>-->
        </el-form>
      </div>
      <div class="custom-content-container" style="height: calc(100% - 208px)">
        <div class="common-header">
          <div><span class="common-head-title">资产视角列表</span></div>
          <div style="width: 50%; margin-left: 8%">
            <attack-stage-text ref="attackStage"/>
          </div>
          <div class="common-head-right">
            <el-row class="tool-container">
              <el-col :span="1.5">
              </el-col>
            </el-row>
          </div>
        </div>
        <el-table height="100%" v-loading="loading" :data="threatenWarnList" @expand-change="expandChange" >
          <el-table-column label="资产IP" min-width="150" prop="victimIp" show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="关联业务系统"  prop="businessApplicationList" width="130">
            <template slot-scope="scope">
              <el-tooltip placement="bottom-end" effect="light" v-if="scope.row.businessApplications && scope.row.businessApplications.length > 0">
                <div slot="content">
                  <div v-for="(item,tagIndex) in scope.row.businessApplications" :key="item.assetId" class="overflow-tag" v-if="tagIndex <= 5">
                    <el-tag type="primary"><span>{{item.assetName}}</span></el-tag>
                  </div>
                  <div v-if="scope.row.businessApplications.length > 5">
                    <el-tag type="primary"><span>...</span></el-tag>
                  </div>
                </div>
                <el-tag type="primary" class="asset-tag"><span>{{handleApplicationTagShow(scope.row.businessApplications)}}</span></el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="所属部门" min-width="120" prop="deptName"  />
          <el-table-column label="最近告警时间" min-width="140" prop="updateTime"  />
          <el-table-column label="安全缺陷"  prop="typeSeg.safeFlaw" width="120">
            <template slot-scope="scope">
              {{ scope.row.typeSeg.safeFlaw || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="扫描探测"  prop="typeSeg.scanProbe" width="120">
            <template slot-scope="scope">
              {{ scope.row.typeSeg.scanProbe || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="攻击尝试"  prop="typeSeg.attackAttempt" width="120">
            <template slot-scope="scope">
              {{ scope.row.typeSeg.attackAttempt || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="漏洞利用"  prop="typeSeg.loopholeMake" width="120">
            <template slot-scope="scope">
              {{ scope.row.typeSeg.loopholeMake || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="木马下载"  prop="typeSeg.trojanVirusDownLoad" width="120">
            <template slot-scope="scope">
              {{ scope.row.typeSeg.trojanVirusDownLoad || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="远程控制"  prop="typeSeg.remoteControl" width="120">
            <template slot-scope="scope">
              {{ scope.row.typeSeg.remoteControl || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="横向渗透"  prop="typeSeg.transverseFiltration" width="120">
            <template slot-scope="scope">
              {{ scope.row.typeSeg.transverseFiltration || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="行动收割"  prop="typeSeg.actionHarvest" width="120">
            <template slot-scope="scope">
              {{ scope.row.typeSeg.actionHarvest || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" :show-overflow-tooltip="false" fixed="right" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetail(scope.row)"
              >查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <el-dialog title="详情" :visible.sync="detailDialog" width="70%" append-to-body>
      <detail-info
        v-if="detailDialog"
        :host-ip="hostIp"
        :detail-type="detailType"
        :dept-name="deptName"
        :is-asset="isAsset"
        style="max-height: 80vh; overflow-x: hidden; overflow-y: auto; padding: 0 30px;"
        :current-asset-data="currentAssetData" />
    </el-dialog>

  </div>
</template>

<script>
  import AttackStage from "../../threat/overview/attackStage";
  import { selectAssetIpOverviewList, selectIpAttackSegNum, selectVictimIpList } from '../../../api/threat/threat'
  import { parseTime } from '@/utils/ruoyi'
  import DetailInfo from "../../frailty/event/component/detail/index.vue"
  import DeptSelect from '@/views/components/select/deptSelect.vue'
  import AttackStageText from '@/views/threat/overview/attackStageText.vue'

  export default {
    name: "AssetThreat",
    components: { AttackStageText, DeptSelect, AttackStage, DetailInfo},
    dicts: ['threaten_type'],
    props: {
      propsQueryParams: {
        type: Object,
        default: function () {
          return null
        }
      },
      currentBtn: {
        type: Number
      }
    },
    data() {
      return {
        showAll: false,
        queryParams: {
          victimIp: '',
          startTime: null,
          endTime: null,
          isAsset: '1',
          alarmLevel: '',
          pageNum: 1,
          pageSize: 10
        },
        rangeTime: [],
        loading: false,
        descLoading: false,
        threatenWarnList: [],
        total: 0,
        childrenData: null,
        descKey: 0,
        detailDialog: false,
        hostIp: '',
        detailType: 'asset',
        deptName: '',
        isAsset: false,
        currentAssetData: {},
      }
    },
    watch: {
      propsQueryParams(val){
        this.handlePropsQuery(val);
      },
    },
    created() {
      let query = this.$route.query;
      if(query && Object.keys(query).length > 0){
        this.handlePropsQuery(query);
      }else {
        this.resetQuery()
        //this.getList()
        this.$nextTick(() => {
          this.$refs.attackStage.initAssetAttackStage(this.queryParams)
        })
      }
    },
    methods: {
      handlePropsQuery(val){
        if(val && Object.keys(val).length > 0){
          this.queryParams = val;
          this.queryParams.isAsset = '1';
          if(val.startTime && val.endTime){
            this.rangeTime = [val.startTime, val.endTime];
          }else {
            /* const start = new Date()
            const end = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            this.rangeTime = [start, end] */
          }
          this.handleQuery();
        }
      },
      handleQuery() {
        this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel
        this.$emit('update:currentBtn',this.queryParams.alarmLevel?parseInt(this.queryParams.alarmLevel) : null)
        this.queryParams = {...this.queryParams,...this.propsQueryParams};
        this.queryParams.pageNum = 1;
        this.queryParams.pageSize = 10;
        this.getList()
        this.$nextTick(() => {
          this.$refs.attackStage.initAssetAttackStage(this.queryParams)
        })
      },
      resetQuery() {
        this.queryParams = {
          victimIp: '',
          startTime: null,
          endTime: null,
          isAsset: '1',
          alarmLevel: '',
          pageNum: 1,
          pageSize: 10
        }
        if (this.rangeTime !== null) {
          this.queryParams.startTime = parseTime(this.rangeTime[0]);
          this.queryParams.endTime = parseTime(this.rangeTime[1]);
        } else {
          this.queryParams.startTime = null;
          this.queryParams.endTime = null;
        }
        if(!this.queryParams.startTime){
          this.queryParams.startTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'); // 一周前，时间部分为 00:00:00
        }
        if(!this.queryParams.endTime){
          this.queryParams.endTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'); // 当前日期，时间部分为 23:59:59
        }
        this.rangeTime = [this.queryParams.startTime, this.queryParams.endTime];
        if(this.$refs.attackStage){
          this.$refs.attackStage.currentSelectedCard = null;
        }
        /* const start = new Date()
        const end = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        this.rangeTime = [start, end] */
        this.handleQuery();
      },
      getList() {
        this.loading = true;
        if (this.rangeTime && this.rangeTime.length === 2) {
          this.queryParams.startTime = parseTime(this.rangeTime[0]);
          this.queryParams.endTime = parseTime(this.rangeTime[1]);
        } else {
          this.queryParams.startTime = null
          this.queryParams.endTime = null
        }
        //同步请求类型统计数据
        let params = {...this.queryParams};
        params.ipOr = this.queryParams.victimIp;
        this.$emit('getList',params);
        selectAssetIpOverviewList(this.queryParams).then(res => {
          if (res.code === 200) {
            this.threatenWarnList = res.rows
            this.threatenWarnList.forEach(e => {
              e.childrenData = {}
            })
            this.total = res.total
          }
        }).finally(()=>{
          this.loading = false;
        })
      },
      async expandChange(row, expandRowKeys) {
        if (expandRowKeys.length > 0) {
          selectIpAttackSegNum({
            ipOr: row.victimIp,
            endTime: this.queryParams.endTime,
            startTime: this.queryParams.startTime,
            alarmLevel: this.queryParams.alarmLevel,
            queryAlarmLevel: this.queryParams.queryAlarmLevel
          }).then(res => {
            if (res.code === 200) {
              const childrenData = res.data
              this.$set(row, 'childrenData', childrenData)
              this.descKey = new Date().getTime()
            }
          })
        }
      },
      handleExport() {
        this.download('system/threadten/exportAsset', {
          ...this.queryParams
        }, `资产威胁_${new Date().getTime()}.xlsx`)

      },
      handleDetail(row) {
        if (row.victimIp) {
        this.detailDialog = true
        this.deptName = row.deptName
        this.hostIp = row.victimIp;
        this.isAsset = row.assetId;
        this.currentAssetData = row;
      }
      },
      handleAtcClick(attackSeg){
        this.queryParams.attackSeg = attackSeg;
        this.getList();
      },
      handleApplicationTagShow(applicationList){
        if(!applicationList || applicationList.length < 1){
          return '';
        }
        let result = applicationList[0].assetName;
        if(applicationList.length > 1){
          result += '...';
        }
        return result;
      },
    }
  }
</script>

<style lang="scss" scoped>
.asset-tag{
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.overflow-tag:not(:first-child){
  margin-top: 5px;
}
::v-deep .el-scrollbar {
  height: 100%;
}
::v-deep .el-dialog__body {
  padding: 20px 0;
}
</style>
