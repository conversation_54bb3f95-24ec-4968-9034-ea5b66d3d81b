<!--网络区域详情-->
<template>
  <el-dialog
    :visible.sync="visible"
    :before-close="handleClose"
    title="网络区域详情"
    width="50%">
    <div class="customForm-container">
      <div class="my-title"><img src="@/assets/images/application/jbxx.png" alt="">基本信息</div>
      <el-descriptions
        class="custom-column"
        direction="vertical"
        size="medium"
        :colon="false"
        label-class-name="custom-label-style"
        content-class-name="custom-content-style"
        :column="3">
        <el-descriptions-item label="网络标识">{{ domain.domainId || '--' }}</el-descriptions-item>
        <el-descriptions-item label="网络名称">{{ domain.domainName || '--' }}</el-descriptions-item>
<!--        <el-descriptions-item label="网络区域全名">{{ domain.domainFullName }}</el-descriptions-item>-->
        <el-descriptions-item label="网络地址">{{ domain.iparea }}</el-descriptions-item>
        <el-descriptions-item label="网络区域状态">
          <dict-tag :options="dict.type.usage_status" :value="domain.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="所属部门">{{ domain.deptName }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ domain.remark }}</el-descriptions-item>

      </el-descriptions>
      <div class="my-title" style="margin: 20px 0"><img src="@/assets/images/application/netinfo.png" alt="">网络区域内资产</div>
      <network-asset ref="reflush7" :all-disabled="true" :networkDomainId="domain.domainId" :tableStyle="tableStyle"/>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {getDomain} from "@/api/dict/domain";
import NetworkAsset from "@/views/dict/domain/networkAsset/index.vue";

export default {
  name: "InternetAreaDetails",
  components: {NetworkAsset},
  dicts:['usage_status'],
  props: {
    internetAreaVisible: {
      type: Boolean,
      default: false
    },
    domainId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      domain: {},
      tableStyle: {
        size: 'mini',
        height: '280',
        border: true,
        'headerRowClassName': 'success-row',
      },
    }
  },
  computed: {
    visible: {
      get() {
        return this.internetAreaVisible
      },
      set(val) {
        this.$emit("update:internetAreaVisible", val);
      }
    }
  },
  watch: {
    domainId: {
      handler(val) {
        this.getDomainInfo(val);
      }
    }
  },
  methods: {
    getDomainInfo(val) {
      getDomain(val).then(res => {
        this.domain = res.data;
        this.$nextTick(()=>{
          this.$refs.reflush7.init();
        })
      })
    },
    handleClose() {
      this.visible = false;
    }
  },
}
</script>

<style scoped lang="scss">
@import "@/assets/styles/customForm";
</style>
