<template>
  <el-dialog
    @open="openDialog"
    title="安全评价详情"
    :visible.sync="dialogVisible"
    class="score-dialog"
    width="50%">
<!--    <div slot="title" class="dialog-title">
      <div class="title"><img src="@/assets/images/overview/if-score-board.png" alt=""/>安全评价详情</div>
    </div>-->
    <div>
      <div class="tabs" style="margin: 10px 0;">
        <el-tabs v-model="activeName">
          <el-tab-pane label="扣分总览" name="overview" />
          <el-tab-pane label="扣分详情" name="detail" />
          <el-tab-pane label="扣分权重" name="weight" />
        </el-tabs>
      </div>
      <PointsOverview ref="overview" :dept-id="deptId" v-if="activeName === 'overview'"/>
      <PointsDetail ref="pointsDetail" v-if="activeName === 'detail'" :edit-btn-visible.sync="editable"/>
      <PointsWeight v-if="activeName === 'weight'"/>
    </div>
    <div v-if="activeName === 'detail'" slot="footer" class="dialog-footer">
      <el-button v-hasPermi="['system:ThreatDeductionStandard:edit']" type="primary" v-if="!editable" @click="enterEditMode">编辑</el-button>
      <el-button type="success" v-if="editable" @click="saveEdit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import PointsOverview from "@/views/threat/overview/component/PointsOverview.vue";
import PointsDetail from "@/views/threat/overview/component/PointsDetail.vue";
import PointsWeight from "@/views/threat/overview/component/PointsWeight.vue";

export default {
  name: "ScoreDetailsDialog",
  components: {PointsWeight, PointsDetail, PointsOverview},
  props: {
    scoreDetailsVisible: {
      type: Boolean,
      required: true,
    },
    deptId: {
      type: [String, Number],
      required: false,
      default: null,
    },
  },
  data() {
    return {
      editable: false,
      scoreDetails: {},
      activeName: 'overview'
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.scoreDetailsVisible
      },
      set(val) {
        this.$emit('update:scoreDetailsVisible', val)
      }
    }
  },
  methods: {
    openDialog() {
      this.$nextTick(() => {
        this.$refs.overview.getList()
        this.$refs.overview.getScoreDetails()
      })
    },
    enterEditMode() {
      this.$refs.pointsDetail.enterEditMode()
    },
    saveEdit() {
      this.$refs.pointsDetail.saveEdit()
    }
  },
}
</script>

<style scoped lang="scss">
.score-dialog {
  ::v-deep.el-dialog__body {
    overflow-y: auto;
    max-height: 80vh;
    padding: 0 20px 30px;
  }
}
.dialog-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid rgba(231,231,231,1);
  .title {
    display: flex;
    font-size: 16px;
    font-weight: 500;
    color: rgba(51,51,51,1);
    img {
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
  }
}

::v-deep .el-dialog__header {
  border-bottom: 1px solid #e4e7ed;
}

::v-deep .el-tabs__nav-wrap::after {
  background-color: #e4e7ed;
}
</style>
