<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-position="right"
          label-width="90px"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="漏洞名称" prop="title">
                <el-input
                  v-model="queryParams.title"
                  placeholder="请输入漏洞名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="漏洞类型" prop="category">
                <!--<el-input-->
                  <!--v-model="queryParams.category"-->
                  <!--placeholder="请输入内容"-->
                  <!--clearable-->
                  <!--@keyup.enter.native="handleQuery"-->
                <!--/>-->
                <el-select
                  v-model="queryParams.category"
                  placeholder="请选择漏洞类型"
                >
                  <el-option
                    v-for="dict in typeList"
                    :key="dict.category"
                    :label="dict.category"
                    :value="dict.category"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="影响目标" prop="webUrl">
                <el-input
                  v-model="queryParams.webUrl"
                  placeholder="请输入影响目标"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleQuery"
                >查询</el-button
                >
                <el-button class="btn2" size="small" @click="resetQuery"
                >重置</el-button
                >
                <el-button class="btn2" size="small" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll">
                  展开
                </el-button>
                <el-button class="btn2" size="small" icon="el-icon-arrow-up" @click="showAll=false" v-else>收起
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10" v-show="showAll">
            <el-col :span="6">
              <el-form-item label="数据来源" prop="dataSource">
                <el-select
                  v-model="queryParams.dataSource"
                  placeholder="请选择数据来源"
                  clearable
                >
                  <el-option :key="1" label="探测" :value="1" />
                  <el-option :key="2" label="手动" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="同步状态" prop="synchronizationstatus">
                <el-select v-model="queryParams.synchronizationStatus" placeholder="请选择同步状态" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.synchronization_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="处置人" prop="status">
                <el-input v-model="queryParams.disposer" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-show="showAll" :gutter="10">
<!--            <el-col :span="24" v-if="rickLevelList.length">
              <el-form-item label="漏洞等级">
                <SystemList
                  ref="systemList2"
                  :systemTypes="rickLevelList"
                  @filterSelect="handleQuery"
                  :systemTypeVal.sync="queryParams.severity"
                />
              </el-form-item>
            </el-col>-->
            <el-col :span="24" v-if="handleStateList.length">
              <el-form-item label="处置状态">
                <SystemList
                  ref="systemList1"
                  :systemTypes="handleStateList"
                  @filterSelect="handleQuery"
                  :systemTypeVal.sync="queryParams.handleState"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">Web漏洞列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleAdd"
                  v-hasPermi="['system:webvulndeal:add']"
                >新增
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  :disabled="multiple"
                  @click="addOrUpdateFlowHandleBatch(null,null)"
                >创建通报
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  :disabled="multiple"
                  @click="showHandleBatch"
                  v-hasPermi="['system:webvulndeal:edit']"
                >批量处置
                </el-button>
              </el-col>
              <!--<el-col :span="1.5">-->
                <!--<el-button-->
                  <!--class="btn1"-->
                  <!--size="small"-->
                  <!--@click="handleScan"-->
                <!--&gt;Web漏洞扫描-->
                <!--</el-button>-->
              <!--</el-col>-->
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleExport"
                >导出
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <el-table  height="100%"  v-loading="loading" :data="webvulnList"  @selection-change="handleSelectionChange">
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
          <el-table-column
            label="漏洞名称"
            prop="title"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column
            label="漏洞类型"
            prop="category"
            width="150"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.loophole_category"
                :value="scope.row.category"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="漏洞等级"
            prop="severity"
            width="120"
          >
            <template slot-scope="scope">
              <el-tag v-if="scope.row.severity === 0" type="info">未知</el-tag>
              <el-tag v-if="scope.row.severity === 1" type="success"
                >低危</el-tag
              >
              <el-tag v-if="scope.row.severity === 2" type="primary"
                >中危</el-tag
              >
              <el-tag v-if="scope.row.severity === 3" type="warning"
                >高危</el-tag
              >
              <el-tooltip v-if="scope.row.severity === 4" placement="top-end" content="严重漏洞" effect="light">
                <el-tag type="danger">严重</el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="影响目标"
            min-width="200"
            prop="webUrl">
            <template slot-scope="scope">
              <span>{{ scope.row.webUrl }}</span>
              <el-tooltip placement="bottom-end" effect="light" v-if="scope.row.assetName && scope.row.assetName.length>0">
                <div slot="content">
                  <div style="display: flex;flex-direction: column">
                    <el-tag type="primary" v-for="item in scope.row.assetName"><span>{{item}}</span></el-tag>
                    <el-tag type="primary" v-if="scope.row.assetName.length > 10"><span>...</span></el-tag>
                  </div>
                </div>
                <el-tag type="primary" class="asset-tag"><span>{{scope.row.assetName[0]}}...</span></el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="所属部门"
            prop="deptName"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.deptName || '-'}}</span>
            </template>
          </el-table-column>
          <el-table-column label="处置人" prop="disposer" width="120" :formatter="disposerFormatter"/>
          <el-table-column label="处置状态" prop="handleState" width="120"  :formatter="handleStateFormatter"/>
          <!--<el-table-column-->
            <!--label="通报状态"-->

            <!--prop="flowState"-->
            <!--width="120"-->
            <!--:formatter="flowStateFormatter"-->
          <!--/>-->

          <el-table-column
            label="数据来源"

            prop="dataSource"
            width="120"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.dataSource == '1'">探测</span>
              <span v-else-if="scope.row.dataSource == '2'">手动</span>
            </template>
          </el-table-column>
          <el-table-column
            label="发现次数"

            prop="scanNum"
            width="120"
          />
          <el-table-column
            label="漏洞更新时间"
            width="160"
            prop="updateTime">
            <template slot-scope="scope">
              {{ parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
            </template>
          </el-table-column>
          <el-table-column label="同步状态" prop="synchronizationStatus" width="120" :formatter="syncStatusFormatter"/>
          <el-table-column
            label="操作"
            width="280"
            fixed="right"
            :show-overflow-tooltip="false"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleDetail(scope.row)"
                v-hasPermi="['system:webvulndeal:query']"
              >详情
              </el-button>
              <el-button
                v-if="scope.row.workId == null&& !(scope.row.handleState === 1 || scope.row.handleState === 3)"
                size="mini"
                type="text"
                @click="handleEdit(scope.row)"
                v-hasPermi="['system:webvulndeal:edit']"
              >编辑
              </el-button>
              <el-button
                v-if="scope.row.workId == null && !(scope.row.handleState === 3)"
                size="mini"
                type="text"
                class="table-delBtn"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:webvulndeal:remove']"
              >删除
              </el-button>
              <el-button
                v-if="scope.row.workId == null && !(scope.row.handleState === 1 || scope.row.handleState === 3)"
                size="mini"
                type="text"
                @click="showHandle(scope.row)"
                v-hasPermi="['system:webvulndeal:edit']"
              >处置
              </el-button>
              <el-button
                v-if="scope.row.flowState == null && (scope.row.handleState === 2 || scope.row.handleState === 0)"
                size="mini"
                type="text"
                @click="addOrUpdateFlowHandle(null, null, scope.row)"
              >创建通报
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 处置威胁情报对话框! -->
    <el-dialog
      title="快速处置"
      :visible.sync="showHandleDialog"
      width="600px"
      append-to-body
    >
      <el-form ref="form" :model="handleForm" :rules="handleRules" label-width="106px">
        <el-form-item label="处置状态" prop="category">
          <el-select
            v-model="handleForm.handleState"
            placeholder="请选择处置状态"
          >
            <el-option
              v-for="dict in handleStateOption"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处置说明" prop="handleDesc">
          <el-input type="textarea" :rows="2" v-model="handleForm.handleDesc" maxlength="120" show-word-limit placeholder="请输入处置说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHandleForm" >确 定</el-button>
        <el-button @click="showHandleDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 处置威胁情报对话框! -->
    <el-dialog
      title="批量处置"
      :visible.sync="showHandleBatchDialog"
      width="600px"
      append-to-body
    >
      <el-form ref="form" :model="handleForm" :rules="handleRules" label-width="106px">
        <el-form-item label="处置状态" prop="category">
          <el-select
            v-model="handleForm.handleState"
            placeholder="请选择处置状态"
          >
            <el-option
              v-for="dict in handleStateOption"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处置说明" prop="handleDesc">
          <el-input type="textarea" :rows="2" v-model="handleForm.handleDesc" maxlength="120" show-word-limit placeholder="请输入处置说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHandleBatchForm" >确 定</el-button>
        <el-button @click="showHandleBatchDialog = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="workDialog"
      title="创建通报"
      :visible.sync="workDialog"
      width="80%"
      append-to-body
    >
      <create-work
        v-if="workDialog"
        :work-type="'2'"
        :m-id="webvulnId"
        @closeWork="closeWork"
      />
    </el-dialog>

    <new-addwebvuln
      :title="title"
      :addWebVulnVisible.sync="showAddwebvuln"
      :webvuln-data="webvulnData"
      :editable="editable"
      @cancel="canceWebvuln()"
      @confirm="confirmWebvuln()"
    />

    <FlowBox v-if="flowVisible" ref="FlowBox" @close="colseFlow" />
    <flow-template-select
      :show.sync="flowTemplateSelectVisible"
      @change="flowTemplateSelectChange"
    />
    <LeakScanDialog
      :title="title"
      :edit-form="editForm"
      edit-title="Web漏洞扫描"
      :is-disabled="isDisabled"
      @getList="getList"
      :scan-strategy-visible.sync="scanStrategyVisible"/>
  </div>
</template>

<script>
import CreateWork from "../../todoItem/todo/createWork";
import newAddwebvuln from "@/views/frailty/webvuln/newAddwebvuln";
import {delWebVuln, listWebVuln} from "@/api/monitor2/webvuln";
import FlowBox from '@/views/zeroCode/workFlow/components/FlowBox.vue'
import FlowTemplateSelect from "@/components/FlowTemplateSelect/index.vue";
import LeakScanDialog from "@/views/safe/server/components/LeakScanDialog.vue";
import { listUser } from "@/api/system/user";
import {
  getHandleStateWebStat,
  getRickLevelWebStat,
  getWebVulnTypeList, handleBatchWeb,
  handleWeb
} from '../../../api/monitor2/webvuln'
import { uniqueArr } from '@/utils'
import {FlowEngineInfo} from "@/api/lowCode/FlowEngine";

export default {
  name: "webvuln",
  props: {
    severity:{
      type: Number,
      default: null
    },
    toParams: {
      type: Object,
      default: () => {}
    }
  },
  components: {LeakScanDialog, CreateWork,newAddwebvuln,FlowBox,FlowTemplateSelect,
    SystemList: () => import('../../../components/SystemList')},
  dicts: [
    'loophole_category',
    'synchronization_status'
  ],
  data() {
    return {
      userList:[],
      showAll: false,
      flowVisible: false,
      flowTemplateSelectVisible: false,
      editable:true,
      webvulnData:null,
      showAddwebvuln:false,
      title:'',
      DealStatusData:{},
      loading: false,
      // 选中数组
      ids: [],
      // 选中数组对象
      rows: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      handleStateOptions: [
        {
          label: '未处置',
          value:  0
        },
        {
          label: '已处置',
          value:  1
        },
        {
          label: '忽略',
          value:  2
        },
        {
          label: '处置中',
          value:  3
        }
      ],
      handleStateOption: [
        {
          label: '已处置',
          value:  1
        },
        {
          label: '忽略',
          value:  2
        }
      ],
      flowStateOptions: [
        {
          label: '待审核',
          value: 0
        },
        {
          label: '待处置',
          value: 1
        },
        {
          label: '待审核',
          value: 2
        },
        {
          label: '待验证',
          value: 3
        },
        {
          label: '已完成',
          value: 4
        },
        {
          label: '待提交',
          value: -1
        },
        {
          label: '未分配',
          value: 99
        }
      ],
      queryParams: {
        title:'',
        category:'',
        severity:'',
        hostIp: '',
        hostPort:'',
        dealStatus: '',
        domainId: '',
        pageNum: 1,
        pageSize: 10
      },
      total: 10,
      showHandleDialog: false,
      showHandleBatchDialog: false,
      handleForm:{
        id:'',
        handleDesc:''
      },
      handleRules:{},
      severity: [],
      webvulnList: [],
      rickLevelList: [],
      handleStateList: [],
      typeList: [],
      openDialog: false,
      vulnId: null,
      workDialog: false,
      webvulnId: '',
      workId: '',
      currentFlowData: null,
      scanStrategyVisible: false,
      editForm: {},
      isDisabled: false,
    }
  },
  created() {
    this.handleQuery();
    this.initList()
  },
  watch: {
    '$route.query': {
      handler(val) {
        if (val){
          this.queryParams.domainId = val.domainId
        }
      },
      deep: true,
      immediate: true
    },
    severity : {
      handler(val) {
        if (val) {
          this.queryParams.severity = val
        }else {
          this.queryParams.severity = null
        }
        this.handleQuery()
      },
      immediate: true
    },
    toParams: {
      handler(newVal) {
        if(newVal && newVal.referenceId){
          this.queryParams.referenceId = newVal.referenceId
          this.handleQuery()
        }
      },
      immediate: true
    }
  },
  methods: {
    handleStateFormatter(row, column, cellValue, index) {
      let name = '未处置';
      let match = this.handleStateOptions.find(item => item.value == cellValue);
      if (match) {
        name = match.label;
      }
      return name;
    },
    disposerFormatter(row, column, cellValue, index){
      let name = '';
      if (cellValue){
        this.userList.forEach(e => {
          if (e.userId == cellValue){
            name = e.nickName
          }
        })
        return name;
      }
      return name;
    },
    syncStatusFormatter(row, column, cellValue, index){
      const label = this.dict.type.synchronization_status.find(item => item.value === cellValue)
      return label ? label.label : ''
    },
    initList() {
      listUser({pageNum:1,pageSize:1000}).then(res=>{
        if (res.rows){
          this.userList = res.rows
        }
      })
      this.handleStateList = []
      this.rickLevelList = []
      this.typeList = []
      getRickLevelWebStat().then(res => {
        res.data.forEach(e => {
          /*if(e.severity === 0) {
            const obj1 = {
              dictValue: 0,
              dictLabel: '未知',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }*/
          if(e.severity === 1) {
            const obj1 = {
              dictValue: 1,
              dictLabel: '低危',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }
          if(e.severity === 2) {
            const obj1 = {
              dictValue: 2,
              dictLabel: '中危',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }
          if(e.severity === 3) {
            const obj1 = {
              dictValue: 3,
              dictLabel: '高危',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }
          if(e.severity === 4) {
            const obj1 = {
              dictValue: 4,
              dictLabel: '严重',
              count: e.num
            }
            this.rickLevelList.push(obj1)
          }
        })
      })

      getHandleStateWebStat().then(res => {
        res.data.forEach(e => {
          if(e.handle_state === 0) {
            const obj1 = {
              dictValue: 0,
              dictLabel: '未处置',
              count: e.num
            }
            this.handleStateList.push(obj1)
          }
          if(e.handle_state === 1) {
            const obj1 = {
              dictValue: 1,
              dictLabel: '已处置',
              count: e.num
            }
            this.handleStateList.push(obj1)
          }
          if(e.handle_state === 2) {
            const obj1 = {
              dictValue: 2,
              dictLabel: '忽略',
              count: e.num
            }
            this.handleStateList.push(obj1)
          }
          if(e.handle_state === 3) {
            const obj1 = {
              dictValue: 3,
              dictLabel: '处置中',
              count: e.num
            }
            this.handleStateList.push(obj1)
          }
        })
      })

      getWebVulnTypeList().then(response=>{
        this.typeList = response.data
      })
    },
    selectable(row) {
      if (row.handleState === 1 || row.handleState === 3) {
        return false
      } else {
        return true
      }
    },
    handleAdd(){
      this.showAddwebvuln=true;
      this.editable=true;
      this.webvulnData=null;
      this.title='新增Web漏洞事件';
    },
    handleEdit(row){
      this.showAddwebvuln=true;
      this.editable=true;
      this.webvulnData={...row};
      this.webvulnData.assetName = ''
      this.title='修改Web漏洞事件';
    },
    handleScan() {
      this.title = '添加任务';
      this.editForm.jobType = 2
      this.scanStrategyVisible = true;
    },
    submitHandleForm(){
      handleWeb(this.handleForm).then(res => {
        this.$message.success("处置成功");
        this.handleForm = {};
        this.showHandleDialog = false;
        this.getList();
        this.initList();
      })
    },
    showHandle(row) {
      this.handleForm = {};
      this.handleForm = {...row};
      if (this.handleForm.handleState === 0 ) {
        this.handleForm.handleState = null
      }
      this.handleForm.assetName = ''
      this.showHandleDialog = true;
    },
    showHandleBatch() {
      let rows = [...this.rows];
      rows = rows.filter(item=>item.handleState === 0 || item.handleState === 2 || item.handleState === null);
      if(rows.length < this.rows.length){
        this.$message.error('选择中有已处置或处置中事件，无法批量处置');
        return false;
      }
      this.handleForm = {};
      if (rows.length === 1) {
        if (rows[0].handleState === 2 ) {
          this.handleForm = rows[0]
        }
      }
      // this.handleForm.id=row.id;
      this.handleForm.ids = this.ids;
      this.showHandleBatchDialog = true;
    },
    submitHandleBatchForm(){
      handleBatchWeb(this.handleForm).then(res => {
        this.$message.success("处置成功");
        this.handleForm = {};
        this.showHandleBatchDialog = false;
        this.getList();
        this.initList();
      })
    },
    handleDelete(row){
      const ids = row.id;
      const vulnNames=row.title;
      this.$modal.confirm('是否确认删除漏洞名称为【' + vulnNames + '】的数据项？').then(function () {
        return delWebVuln(ids);
      }).then(() => {
        this.getList();
        this.initList()
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    canceWebvuln(){
        this.showAddwebvuln=false;
    },
    confirmWebvuln(){
      this.showAddwebvuln=false;
      this.getList();
      this.initList()
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize=10;
      this.total = 0;
      this.getList();
    },
    resetQuery() {
      this.queryParams={
        severity: this.$props.severity,
        pageNum: 1,
        pageSize: 10,
        referenceId: '',
      };
      this.$refs.systemList1 && this.$refs.systemList1.resetSelection();
      this.$refs.systemList2 && this.$refs.systemList2.resetSelection();
      this.getList();
    },
    getList() {
      this.loading = true;
      listWebVuln(this.queryParams).then(response=>{
        if(response.rows && response.rows.length >0){
          response.rows.forEach(item => {
            if(item.assetName){
              item.assetName = uniqueArr(item.assetName.split(','));
            }
            if(item.deptName){
              let deptNameArr = uniqueArr(item.deptName.split(','));
              item.deptName = deptNameArr.join(',');
            }
          })
        }
        this.webvulnList=response.rows;
        this.total=response.total;
        this.loading=false;
      });
    },
    handleExport() {
      this.download('/monitor2/webvulndeal/export', {
        ...this.queryParams
      }, `Web漏洞事件_${new Date().getTime()}.xlsx`)
    },
    flowStateFormatter(row, column, cellValue, index){
      let name = '未分配';
      let match = this.flowStateOptions.find(item => item.value==cellValue);
      if(match){
        name = match.label;
      }
      return name;
    },
    handleDetail(row) {
      this.showAddwebvuln=true;
      this.editable=false;
      this.webvulnData={...row};
      this.webvulnData.assetName = ''
      this.title='查看Web漏洞事件';
    },
    createWork(row) {
      this.webvulnId = row.id
      this.workDialog = true
    },
    closeWork() {
      this.workDialog = false
    },
    flowTemplateSelectChange(val){
      this.flowTemplateSelectVisible = false;
      this.flowVisible = true;
      this.currentFlowData.flowId = val;
      this.$nextTick(() => {
        this.$refs.FlowBox.init(this.currentFlowData)
      })
    },
    addOrUpdateFlowHandle(id, flowState,row) {
      let data = {
        id: id || '',
        // flowtemplatejson id
        formType: 1,
        opType: flowState ? 0 : '-1',
        status: flowState,
        row: row,
        isWork: true
      }
      data.row.workType='2';
      data.row.eventType = 2;
      data.originType = 'event';
      this.currentFlowData = data;
      this.loading = true;
      this.getConfigKey("default.flowTemplateId").then(res => {
        let flowId = res.msg;
        if(flowId){
          this.getFlowEngineInfo(flowId);
        }else {
          this.flowTemplateSelectVisible = true;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    addOrUpdateFlowHandleBatch(id, flowState) {
      let rows = [...this.rows];
      rows = rows.filter(item=>item.handleState === 0 || item.handleState === null);
      if(!rows || rows.length < 1){
        this.$message.error('未选择未处置事件，无法批量创建通报');
        return false;
      }
      let data = {
        id: id || '',
        formType: 1,
        opType: flowState ? 0 : '-1',
        status: flowState,
        rows: rows,
        isWork: true
      }
      data.rows[0].workType='2';
      data.rows[0].eventType = 2;
      data.originType = 'event';
      this.currentFlowData = data;
      this.loading = true;
      this.getConfigKey("default.flowTemplateId").then(res => {
        let flowId = res.msg;
        if(flowId){
          this.getFlowEngineInfo(flowId);
        }else {
          this.flowTemplateSelectVisible = true;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    getFlowEngineInfo(val){
      FlowEngineInfo(val).then(res => {
        if(res.data && res.data.flowTemplateJson){
          let data = JSON.parse(res.data.flowTemplateJson);
          if(!data[0].flowId){
            this.$message.error('该流程模板异常,请重新选择');
          }else {
            this.currentFlowData.flowId = data[0].flowId;
            this.flowVisible = true;
            this.$nextTick(() => {
              this.$refs.FlowBox.init(this.currentFlowData);
            });
          }
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
      this.rows = selection;
    },
    colseFlow(isrRefresh) {
      this.flowVisible = false
      if (isrRefresh) this.getList(); this.initList();
    },
  }
}
</script>
<style lang="scss" scoped>
@import "../../../assets/styles/assetIndex.scss";
  .loop_dialog ::v-deep.el-dialog__body {
    padding: 30px 50px 20px;
  }

@media screen and (min-height: 900px) {
  .loop_dialog ::v-deep.el-dialog {
    width: 50% !important;
  }
}
  .asset-tag{
    margin-left: 5px;
    max-width: 35%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
  }
.el-tooltip__popper{
  font-size: 12px;
  max-width:300px;
}
</style>
