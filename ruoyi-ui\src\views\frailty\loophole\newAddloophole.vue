<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="60%"
    append-to-body>
    <div class="m_over">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px" :disabled="!editable">
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap;margin-bottom: 10px;">
          <el-col :span="24" class="mb8">
            <el-divider direction="vertical"></el-divider>
            <div class="my-title">影响目标信息</div>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产名称" prop="assetName">
              <el-input v-model="form.assetName" placeholder="请选择资产" :disabled="loopholeData!=null?true:false"
                        @focus="openAsset()"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产类型" prop="assetTypeDesc">
              <el-input v-model="form.assetTypeDesc" placeholder="请先选择资产" :disabled="true"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="影响目标" prop="hostIp">
              <el-tag v-model="form.hostIp" v-if="form.hostIp">{{ form.hostIp }}</el-tag>
              <el-input v-model="form.hostIp" placeholder="请先选择资产" v-else :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发现时间" prop="createTime">
              <el-date-picker style="width: 100%;"
                              v-model="form.createTime"
                              type="date"
                              placeholder="选择发现时间"
                              format="yyyy 年 MM 月 dd 日"
                              value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口" prop="hostPort">
              <el-input v-model="form.hostPort" placeholder="请输入端口"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务" prop="protocol">
              <el-input v-model="form.protocol" placeholder="请输入服务"/>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="mb8">
            <el-divider direction="vertical"></el-divider>
            <div class="my-title">漏洞详情</div>
          </el-col>
          <el-form ref="bssVulnInfo" :model="bssVulnInfo" :rules="VulnInfoRules" label-width="130px"
                   :disabled="!editable">
            <el-col :span="21">
              <el-form-item label="漏洞名称" prop="title">
                <el-input v-model="bssVulnInfo.title" placeholder="请输入漏洞名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button type="primary" style="width: 100%" @click="selectVulnInfo()">选择漏洞情报</el-button>
            </el-col>
            <el-col :span="12">
              <el-form-item label="漏洞类型" prop="category">
                <!--<el-input v-model="bssVulnInfo.category" placeholder="请输入漏洞类型"/>-->
                <el-select v-model="bssVulnInfo.category" filterable placeholder="请选择漏洞类型">
                  <el-option
                    v-for="dict in typeList"
                    :key="dict.category"
                    :label="dict.category"
                    :value="dict.category"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="漏洞等级" prop="severity">
                <el-select v-model="bssVulnInfo.severity" placeholder="请选择漏洞等级" clearable>
                  <el-option
                    v-for="item in severityMap"
                    :key="item.severity"
                    :label="item.value"
                    :value="item.severity"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="漏洞编号" prop="exposures">
                <el-input v-model="bssVulnInfo.exposures" placeholder="请输入漏洞编号"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="CVSS风险评分" prop="cvss">
                <el-input v-model="bssVulnInfo.cvss" placeholder="请输入cvss评分"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="漏洞披露时间" prop="publishedDateTime">
                <el-date-picker style="width: 100%;"
                                v-model="bssVulnInfo.publishedDateTime"
                                type="date"
                                placeholder="选择漏洞披露时间"
                                format="yyyy 年 MM 月 dd 日"
                                value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <!--              <el-col :span="24">
                        <el-form-item label="标签" prop="tags">
                          <Dynamic-Tag v-model="bssVulnInfo.tags"/>
                        </el-form-item>
                      </el-col>-->
            <el-col :span="24">
              <el-form-item label="漏洞描述" prop="summary">
                <el-input :autosize="{minRows: 3, maxRows: 3}" v-model="bssVulnInfo.summary" type="textarea"
                          placeholder="请输入漏洞描述"/>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="漏洞危害" prop="impact">
                <el-input :autosize="{minRows: 3, maxRows: 3}" v-model="bssVulnInfo.impact" type="textarea"
                          placeholder="请输入漏洞危害"/>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="isDeatil">
              <el-form-item label="漏洞细节" prop="detail">
                <el-input :autosize="{minRows: 3, maxRows: 3}" v-model="bssVulnInfo.detail" type="textarea"
                          placeholder="请输入漏洞细节"/>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="修改方案" prop="solution">
                <el-input :autosize="{minRows: 3, maxRows: 3}" v-model="bssVulnInfo.solution" type="textarea"
                          placeholder="请输入修改方案"/>
              </el-form-item>
            </el-col>
          </el-form>

          <el-col v-if="form.fileUrl!=null||editable">
            <el-col :span="24" class="mb8">
              <el-divider direction="vertical"></el-divider>
              <div class="my-title">文件上传</div>
            </el-col>
            <el-col :span="24">
              <el-form-item label="上传文件" prop="fileUrl">
                <file-upload v-model="form.fileUrl"
                             :disUpload="!editable"
                             :limit="5"
                             :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                />
              </el-form-item>
            </el-col>
          </el-col>

        </el-row>
      </el-form>
      <!--   工单处理流程   -->
      <div v-if="workHistoryTable.length>0" style="max-height: 300px;overflow-y: auto">
        <div>
          <el-divider direction="vertical"></el-divider>
          <div class="my-title">工单流程</div>
        </div>
        <div style="margin-top: 20px">
          <!--        <work-history :history-list="workHistoryTable" />-->
          <el-timeline :reverse="false" style="margin-left: 5px">
            <!--            0-拒绝、1-同意、2-提交、3-撤回、4-终止、5-指派、6-加签、7-转办-->
            <el-timeline-item
              v-for="(activity, index) in workHistoryTable"
              :key="index" placement="bottom"
              :type="activity.handleStatus==1?'success':activity.handleStatus==0?'danger':''">
              <!--              <div class="m-timeline">
                              <span v-if="activity.nodeName">{{activity.nodeName}}</span>
                            </div>
                            <div style="padding-left: 50px;">
                              <span>处理人：</span><span>{{ activity.userName }}</span>
                            </div>-->
              <div :class="index==workHistoryTable.length-1?'m-timeline-last m-timeline':'m-timeline'">
                <div class="node-name">
                  {{ activity.nodeName }}
                </div>
                <div class="handle-info">
                  <div class="handle-user">处理人: {{ activity.userName }}</div>
                  <div class="handle-result">
                    {{ activity.handleStatus == 1 ? '结果：同意' : activity.handleStatus == 0 ? '结果：拒绝' : '' }}
                  </div>
                </div>
                <div class="handle-time">
                  时间：{{ activity.handleTime ? parseTime(new Date(activity.handleTime)) : '' }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <el-row v-if="loopholeData !== null && loopholeData.id !== null" :gutter="10" type="flex"
              style="flex-wrap: wrap;margin-bottom: 10px;">
        <el-col :span="24" class="mb8">
          <el-divider direction="vertical"></el-divider>
          <div class="my-title">原始日志</div>
        </el-col>
        <el-col :span="24" class="mb8">
          <SrclogDetail :bussinessid="loopholeData.id" bussinessTable="monitor_bss_vuln_deal"/>
        </el-col>
      </el-row>
    </div>
    <el-dialog v-if="showVulnInfo" title="选择漏洞情报" :visible.sync="showVulnInfo" class="asset_dialog" width="50%" append-to-body>
      <gap-intel v-if="showVulnInfo" :is-select="true" ref="gapInfo" @confirm="confirmVulnInfo"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="gainGapData">确 定</el-button>
        <el-button @click="showVulnInfo = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog v-if="showAssetSelect" title="选择资产" :visible.sync="showAssetSelect" class="asset_dialog" width="66%"
               append-to-body>
      <asset-select v-if="showAssetSelect" ref="assetInfo" @cancel="showAssetSelect = false"
                    @assetSelected="getAssetInfo"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="gainAssetInfo">确 定</el-button>
        <el-button @click="showAssetSelect = false">取 消</el-button>
      </span>
    </el-dialog>
    <span slot="footer" class="dialog-footer">
      <el-button v-if="editable" type="primary" class="mt10" @click="submitForm">确认</el-button>
      <el-button plain class="mt10" @click="$emit('cancel')">取消</el-button>
    </span>
 </el-dialog>
</template>

<script>
import DynamicTag from '@/components/DynamicTag';
import WorkHistory from "@/views/todoItem/todo/workHistory.vue";
import FileUpload from "@/components/FileUpload/index";
import gapIntel from "@/views/basis/gap/gapIntel.vue";
import {addVulnDeal, getVulnDealDetail, updateVulnDeal} from "@/api/monitor2/assetFrailty";
import assetSelect from "@/views/assetSelect/index.vue";
import {FlowBeforeInfo} from "@/api/lowCode/FlowBefore";
import SrclogDetail from "@/views/SafetyAssessments/srclog/detailComponent";
import {getHostVulnTypeList} from '../../../api/monitor2/vulnResult'

export default {
  name: "newAddloophole",
  components: {DynamicTag, FileUpload, gapIntel, WorkHistory, assetSelect, SrclogDetail},
  dicts: [
    'loophole_category'
  ],
  props: {
    editable: {
      type: Boolean,
      default: true
    },
    loopholeData: {
      type: Object,
      default: null
    },
    addLoopholeVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增漏洞'
    }
  },
  data() {
    return {
      showAssetSelect: false,
      workHistoryTable: [],
      showVulnInfo: false,
      form: {
        assetName: ''
      },
      bssVulnInfo: {},
      rules: {
        assetName: [
          {required: true, message: '请选择资产', trigger: 'blur'},
        ],
        createTime: [
          {required: true, message: '请输入发现时间', trigger: 'blur'},
        ],
        hostPort: [
          {required: false, min: 0, max: 11, message: '端口不能超过11字符', trigger: 'blur'},
          {
            required: true,
            pattern: /^([0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$/,
            message: '端口不能为空或格式不正确',
            trigger: 'blur'
          },
        ],
        protocol: [
          {required: true, min: 0, max: 20, message: '服务为空或者大小超过 20 个字符', trigger: 'blur'},
          {
            required: false,
            pattern: '^[^\\s]*$',
            message: '请勿输入空格！',
            trigger: 'blur'
          },
          {
            required: false,
            pattern: /^[A-Za-z]+$/,
            message: '服务格式有误！',
            trigger: 'blur'
          }
        ],
      },
      VulnInfoRules: {
        title: [
          {required: false, min: 0, max: 300, message: '漏洞名称不能超过300字符', trigger: 'blur'},
          {required: true, message: '请输入漏洞名称', trigger: 'blur'},
          {
            required: true,
            pattern: /^[^\s]+/,
            message: '不能以空格开头！',
            trigger: 'blur'
          }
        ],
        category: [
          {required: false, min: 0, max: 50, message: '漏洞类型不能超过 50 个字符', trigger: 'blur'},
          {required: true, message: '请输入漏洞类型', trigger: 'blur'},
          {
            required: false,
            pattern: /^[^\s]+/,
            message: '不能以空格开头！',
            trigger: 'blur'
          }
        ],
        severity: [
          {required: true, message: '请输入漏洞等级', trigger: 'blur'},
        ],
        exposures: [
          {required: false, min: 0, max: 200, message: '漏洞编号不能超过200字符', trigger: 'blur'},
          {
            required: false,
            pattern: /^[^\s]+/,
            message: '不能以空格开头！',
            trigger: 'blur'
          }
        ],
        cvss: [
          {required: false, min: 0, max: 50, message: 'cvss评分不能超过50字符', trigger: 'blur'},
          {
            required: false,
            pattern: /^[^\s]+/,
            message: '不能以空格开头！',
            trigger: 'blur'
          }
        ],
        summary: [
          {required: false, min: 0, max: 4000, message: '漏洞描述不能超过4000字符', trigger: 'blur'},
          {
            required: false,
            pattern: /^[^\s]+/,
            message: '不能以空格开头！',
            trigger: 'blur'
          }
        ],
        impact: [
          {required: false, min: 0, max: 4000, message: '漏洞危害不能超过4000字符', trigger: 'blur'},
          {
            required: false,
            pattern: /^[^\s]+/,
            message: '不能以空格开头！',
            trigger: 'blur'
          }
        ],
        detail: [
          {required: false, min: 0, max: 4000, message: '漏洞细节不能超过4000字符', trigger: 'blur'},
          {
            required: false,
            pattern: /^[^\s]+/,
            message: '不能以空格开头！',
            trigger: 'blur'
          }
        ],
        solution: [
          {
            required: false,
            pattern: /^[^\s]+/,
            message: '不能以空格开头！',
            trigger: 'blur'
          }
        ],
      },
      loading: false,
      typeList: [],
      severityMap: [
        {severity: 1, value: "低危"},
        {severity: 2, value: "中危"},
        {severity: 3, value: "高危"},
        {severity: 4, value: "严重"}
      ],
      isDeatil: true
    }
  },
  mounted() {
    this.initData()
    if (this.loopholeData != null) {
      this.handleUpdate(this.loopholeData, this.editable);
      this.getWorkHistoryList();
    }
  },
  computed: {
    visible: {
      get() {
        return this.addLoopholeVisible
      },
      set(val) {
        this.$emit("update:addLoopholeVisible", val);
      }
    }
  },
  methods: {
    initData() {
      this.typeList = []
      getHostVulnTypeList().then(res => {
        this.typeList = res.data
      })
    },
    getWorkHistoryList() {
      /*const that = this
      that.workHistoryTable=[];
      if (this.loopholeData.workId!=null) {
        listHistory({
          workId: this.loopholeData.workId
        }).then(res => {
          if (res.code === 200) {
            that.workHistoryTable = res.rows;
          }
        })
      }*/
      if (this.loopholeData.prodId != null) {
        this.getFlowRecords(this.loopholeData.prodId);
      }
    },
    getFlowRecords(prodId) {
      FlowBeforeInfo(prodId).then(res => {
        this.workHistoryTable = res.data.flowTaskOperatorRecordList;
        if (res.data.flowTaskInfo && res.data.flowTaskInfo.thisStepId == 'end') {
          let endNode = {...this.workHistoryTable[this.workHistoryTable.length - 1]};
          endNode.nodeName = '结束';
          endNode.handleStatus = null;
          this.workHistoryTable.push(endNode);
        }
      })
    },
    confirmVulnInfo(form) {
      this.bssVulnInfo = form;
      this.showVulnInfo = false;
    },
    /** 修改及查看按钮操作 */
    handleUpdate(id, edit) {
      this.reset();
      this.editable = edit;
      this.isDeatil = edit
      let queryParams = {...this.loopholeData};
      delete queryParams.businessApplications;
      getVulnDealDetail(queryParams).then(response => {
        this.form = response.data[0];
        if (this.form.hostPort != null) {
          this.form.hostPort = (this.form.hostPort).toString();
        }
        if (this.form != null && this.form.bssVulnInfo != null) {
          this.bssVulnInfo = this.form.bssVulnInfo;
        }
      });
    },
    reset() {
      this.$refs.form.resetFields();
      this.$refs.bssVulnInfo.resetFields();
    },
    /** 提交按钮 */
    submitForm() {
      let pass = true;
      if (this.form.id) {
        let valids = ["createTime", "hostPort", "protocol"];
        for (const item of valids) {
          this.$refs.form.validateField(item, (valid) => {
            if (pass == true || pass.length == undefined || pass.length == 0) {
              pass = valid;
            }
          });
        }
        if (pass.length > 0) {
          return;
        }
      } else {
        this.$refs.form.validate(valid => pass = valid);
        if (!pass) {
          return;
        }
      }
      this.$refs.bssVulnInfo.validate(valid => pass = valid)
      if (!pass) {
        return;
      }
      this.form.bssVulnInfo = this.bssVulnInfo;
      if (this.loopholeData != null) {
        updateVulnDeal(this.form).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.$emit('confirm');
        });
      } else {
        addVulnDeal(this.form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.$emit('confirm');
          this.reset()
        });
      }
    },
    selectVulnInfo() {
      this.showVulnInfo = true;
    },
    openAsset() {
      this.showAssetSelect = true;
    },
    getAssetInfo(form) {
      this.form.assetName = form[0].assetName;
      if (this.form.assetName != null) {
        this.$refs.form.validateField('assetName', (valid) => {

        })
      }
      this.form.assetTypeDesc = form[0].assetTypeDesc;
      this.form.hostIp = form[0].ip;
      this.showAssetSelect = false;
    },
    gainAssetInfo() {
      this.$refs.assetInfo.submit()
    },
    cancel() {
      this.showVulnInfo = false;
    },
    gainGapData() {
      this.$refs.gapInfo.confirmData()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  overflow: auto;
  overflow-x: hidden;
  max-height: 80vh;
}

.el-divider {
  background: #0E94EA;
}

.el-divider--vertical {
  display: inline-block;
  width: 5px;
  height: 2em;
  margin: 0 8px 0 0;
  vertical-align: middle;
  position: relative;
}

.hr.el-divider {
  margin: 0;
  background: #DCDFE6;
  border: 1px solid #DCDFE6;
}

.my-title {
  display: inline-block;
  vertical-align: center;
}

.app-container {
  min-height: unset;
  -webkit-box-shadow: unset;
  box-shadow: unset;
  border-radius: unset;
}


.asset_dialog {
  overflow: hidden;
}

.m_over::-webkit-scrollbar {
  width: 4px;
}
</style>
