<!--扣分权重-->
<template>
  <div class="points-weight-container">
    <table class="points-weight-table">
      <thead>
      <tr>
        <th colspan="6">九紫平台风险/威胁等级扣分权重</th>
      </tr>
      <tr>
        <th></th>
        <th></th>
        <th>严重</th>
        <th>高危</th>
        <th>中危</th>
        <th>低危</th>
      </tr>
      </thead>
      <tbody>
      <template v-for="(category, categoryIndex) in weightData">
        <tr v-for="(item, itemIndex) in category.items" :key="`${categoryIndex}-${itemIndex}`">
          <td :rowspan="category.items.length" v-if="itemIndex === 0">{{ category.category }}</td>
          <td>{{ item.type }}</td>
          <td>{{ item.critical }}</td>
          <td>{{ item.high }}</td>
          <td>{{ item.mediumHazard }}</td>
          <td>{{ item.low }}</td>
        </tr>
        <tr v-if="categoryIndex < weightData.length - 1"><td colspan="6"></td></tr>
      </template>
      </tbody>
    </table>
  </div>
</template>


<script>
import { listThreatDeductionStandard } from "@/api/aqsoc/threat-deduction-standard/ThreatDeductionStandard";
export default {
  name: "PointsWeight",
  data() {
    return {
      weightData: [

      ]
    };
  },
  created() {
    this.ThreatDeductionStandard();
  },
  methods: {
    ThreatDeductionStandard() {
      listThreatDeductionStandard({}).then(response => {
        this.weightData = response.rows;
      });
    },
  }
}
</script>


<style scoped lang="scss">
.points-weight-container {
  width: 100%;
  overflow-x: auto;
}

.points-weight-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
  }

  td {
    height: 48px;
    color: #333333;
  }

  thead {
    tr:first-child th {
      height: 48px;
      background-color: #0074cc;
      color: rgba(255,255,255,1);
      font-size: 26px;
    }

    tr:nth-child(2) th {
      height: 48px;
      background-color: #f0f0f0 ;
      color: #707170 ;
    }
  }

/*  tbody {
    tr:nth-child(even) {
      background-color: #f2f2f2;
    }

    tr:hover {
      background-color: #ddd;
    }
  }*/

  .points-weight-table td[rowspan],
  .points-weight-table th[rowspan] {
    vertical-align: middle;
  }
}
</style>
