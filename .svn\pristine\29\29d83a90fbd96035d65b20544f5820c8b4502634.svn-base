package com.ruoyi.threaten.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.ffsafe.api.domain.FfsafeFlowDetail;
import com.ruoyi.ffsafe.api.service.IFfsafeFlowDetailService;
import com.ruoyi.ffsafe.component.FFSafeRequestComponent;
import com.ruoyi.rabbitmq.domain.SyncMessage;
import com.ruoyi.rabbitmq.enums.DataTypeEnum;
import com.ruoyi.rabbitmq.enums.OperationTypeEnum;
import com.ruoyi.rabbitmq.service.IHandleDataSyncSender;
import com.ruoyi.safe.domain.TblServer;
import com.ruoyi.safe.service.ITblServerService;
import org.springframework.scheduling.annotation.Async;
import com.ruoyi.threaten.domain.TblAttackAlarm;
import com.ruoyi.threaten.domain.TblAttackAlarmTags;
import com.ruoyi.threaten.mapper.TblAttackAlarmMapper;
import com.ruoyi.threaten.service.ITblAttackAlarmService;
import com.ruoyi.threaten.service.ITblAttackAlarmTagsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 攻击者视角告警列Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
@Slf4j
public class TblAttackAlarmServiceImpl implements ITblAttackAlarmService
{
    @Autowired
    private TblAttackAlarmMapper tblAttackAlarmMapper;
    @Resource
    private RedisCache redisCache;
    @Resource
    private ITblAttackAlarmTagsService attackAlarmTagsService;
    @Resource
    private IFfsafeFlowDetailService flowDetailService;
    @Resource
    private ITblServerService serverService;
    @Resource
    private IHandleDataSyncSender handleDataSyncSender;
    @Resource
    private FFSafeRequestComponent ffSafeRequestComponent;

    /**
     * 查询攻击者视角告警列
     *
     * @param id 攻击者视角告警列主键
     * @return 攻击者视角告警列
     */
    @Override
    public TblAttackAlarm selectTblAttackAlarmById(Long id)
    {
        return tblAttackAlarmMapper.selectTblAttackAlarmById(id);
    }

    /**
     * 批量查询攻击者视角告警列
     *
     * @param ids 攻击者视角告警列主键集合
     * @return 攻击者视角告警列集合
     */
    @Override
    public List<TblAttackAlarm> selectTblAttackAlarmByIds(Long[] ids)
    {
        return tblAttackAlarmMapper.selectTblAttackAlarmByIds(ids);
    }

    /**
     * 查询攻击者视角告警列列表
     *
     * @param tblAttackAlarm 攻击者视角告警列
     * @return 攻击者视角告警列
     */
    @Override
    public List<TblAttackAlarm> selectTblAttackAlarmList(TblAttackAlarm tblAttackAlarm)
    {
        return tblAttackAlarmMapper.selectTblAttackAlarmList(tblAttackAlarm);
    }

    /**
     * 新增攻击者视角告警列
     *
     * @param tblAttackAlarm 攻击者视角告警列
     * @return 结果
     */
    @Override
    public int insertTblAttackAlarm(TblAttackAlarm tblAttackAlarm)
    {
        return tblAttackAlarmMapper.insertTblAttackAlarm(tblAttackAlarm);
    }

    /**
     * 修改攻击者视角告警列
     *
     * @param tblAttackAlarm 攻击者视角告警列
     * @return 结果
     */
    @Override
    public int updateTblAttackAlarm(TblAttackAlarm tblAttackAlarm)
    {
        tblAttackAlarm.setUpdateTime(DateUtils.getNowDate());
        return tblAttackAlarmMapper.updateTblAttackAlarm(tblAttackAlarm);
    }

    /**
     * 删除攻击者视角告警列信息
     *
     * @param id 攻击者视角告警列主键
     * @return 结果
     */
    @Override
    public int deleteTblAttackAlarmById(Long id)
    {
        return tblAttackAlarmMapper.deleteTblAttackAlarmById(id);
    }

    /**
     * 批量删除攻击者视角告警列
     *
     * @param ids 需要删除的攻击者视角告警列主键
     * @return 结果
     */
    @Override
    public int deleteTblAttackAlarmByIds(Long[] ids)
    {
        return tblAttackAlarmMapper.deleteTblAttackAlarmByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sync(List<TblAttackAlarm> attackAlarmList) {
        if(CollUtil.isEmpty(attackAlarmList)){
            return;
        }
        TimeInterval timer = DateUtil.timer();
        //统计次数
        TblAttackAlarm first = attackAlarmList.get(0);
        TblAttackAlarm last = attackAlarmList.get(attackAlarmList.size() - 1);
        Date queryStartTime = last.getStartTime();
        Date queryEndTime = first.getUpdateTime();
        List<String> ips = attackAlarmList.stream().map(TblAttackAlarm::getAttackIp).collect(Collectors.toList());
        FfsafeFlowDetail queryFlowDetail = new FfsafeFlowDetail();
        queryFlowDetail.setStartTime(queryStartTime);
        queryFlowDetail.setEndTime(queryEndTime);
        Map<String, Object> queryFlowDetailParams = new HashMap<>();
        queryFlowDetailParams.put("ips",ips);
        queryFlowDetail.setParams(queryFlowDetailParams);
        List<FfsafeFlowDetail> detailList = flowDetailService.selectList(queryFlowDetail);
        if(CollUtil.isNotEmpty(detailList)){
            attackAlarmList.forEach(attackAlarm -> {
                List<FfsafeFlowDetail> matchDetails = detailList.stream().filter(detail -> attackAlarm.getAttackIp().equals(detail.getSip())
                                && detail.getCreateTime().getTime() >= attackAlarm.getStartTime().getTime() && detail.getCreateTime().getTime() <= attackAlarm.getUpdateTime().getTime())
                        .collect(Collectors.toList());
                if(CollUtil.isNotEmpty(matchDetails)){
                    attackAlarm.setAttackNums((long) matchDetails.size()); //攻击次数
                    attackAlarm.setAttackTypeNums(matchDetails.stream().map(FfsafeFlowDetail::getThreatenName).distinct().count()); //命中规则数
                    attackAlarm.setVictimIpNums(matchDetails.stream().map(FfsafeFlowDetail::getDip).distinct().count()); //攻击目标IP数
                }
            });
        }

        List<TblAttackAlarm> saveList = new ArrayList<>();
        List<TblAttackAlarm> updateList = new ArrayList<>();
        //查询原有的
        TblAttackAlarm queryOldAttackAlarm = new TblAttackAlarm();
        Map<String,Object> params = new HashMap<>();
        params.put("ips",CollUtil.distinct(ips));
        queryOldAttackAlarm.setParams(params);
        List<TblAttackAlarm> oldAttackAlarmList = tblAttackAlarmMapper.selectTblAttackAlarmList(queryOldAttackAlarm);
        if(CollUtil.isNotEmpty(oldAttackAlarmList)){
            attackAlarmList.forEach(attackAlarm -> {
                TblAttackAlarm matchOld = oldAttackAlarmList.stream().filter(oldAttackAlarm -> attackAlarm.getAttackIp().equals(oldAttackAlarm.getAttackIp()) && attackAlarm.getStartTime().getTime() == oldAttackAlarm.getStartTime().getTime())
                        .findFirst().orElse(null);
                if(matchOld != null){
                    attackAlarm.setId(matchOld.getId());
                    updateList.add(attackAlarm);
                }else {
                    saveList.add(attackAlarm);
                }
            });
        }else {
            saveList.addAll(attackAlarmList);
        }

        if(CollUtil.isNotEmpty(saveList)){
            //批量新增
            log.info("批量新增攻击者视角告警列开始: {}", saveList.size());
            tblAttackAlarmMapper.batchInsert(saveList);
        }
        if(CollUtil.isNotEmpty(updateList)){
            //批量更新
            log.info("批量更新攻击者视角告警列开始: {}", updateList.size());
            tblAttackAlarmMapper.batchUpdate(updateList);
        }
        //保存tags
        List<TblAttackAlarmTags> tagsList = new ArrayList<>();
        attackAlarmList.forEach(attackAlarm -> {
            List<TblAttackAlarmTags> tags = attackAlarm.getTags();
            if(CollUtil.isNotEmpty(tags)){
                tagsList.addAll(tags.stream().peek(tag -> tag.setAttackId(attackAlarm.getId())).collect(Collectors.toList()));
            }
        });
        if(CollUtil.isNotEmpty(tagsList)){
            attackAlarmTagsService.batchInsert(tagsList);
        }
        // 组合成attackId与标签列表的Map（保持顺序）
        Map<Long, List<TblAttackAlarmTags>> attackIdToTagsMap = new LinkedHashMap<>();
        if(CollUtil.isNotEmpty(tagsList)){
            attackIdToTagsMap = tagsList.stream().collect(Collectors.groupingBy(
                TblAttackAlarmTags::getAttackId,
                LinkedHashMap::new,
                Collectors.toList()
            ));
        }

        // 发送MQ消息，并为每个告警设置对应的标签
        if(CollUtil.isNotEmpty(saveList)){
            for(TblAttackAlarm alarm : saveList){
                // 设置标签
                alarm.setTags(attackIdToTagsMap.get(alarm.getId()));
                // 异步发送INSERT消息
                sendDataSyncMessageAsync(alarm, OperationTypeEnum.INSERT);
            }
        }
        if(CollUtil.isNotEmpty(updateList)){
            for(TblAttackAlarm alarm : updateList){
                // 设置标签
                alarm.setTags(attackIdToTagsMap.get(alarm.getId()));
                // 异步发送UPDATE消息
                sendDataSyncMessageAsync(alarm, OperationTypeEnum.UPDATE);
            }
        }
        log.info("同步攻击者总耗时：{}", timer.interval());
    }



    @Override
    public JSONObject getDipDetails(TblAttackAlarm tblAttackAlarm) {
        JSONObject resultObject = new JSONObject();
        List<JSONObject> result = new ArrayList<>();
        FfsafeFlowDetail query = new FfsafeFlowDetail();
        query.setSip(tblAttackAlarm.getAttackIp());
        query.setStartTime(tblAttackAlarm.getStartTime());
        query.setEndTime(tblAttackAlarm.getUpdateTime());
        List<FfsafeFlowDetail> flowDetailList = flowDetailService.selectList(query);

        if(CollUtil.isNotEmpty(flowDetailList)){
            //根据dip字段去重，并赋值给flowDetailList
            flowDetailList = flowDetailList.stream().collect(Collectors.groupingBy(FfsafeFlowDetail::getDip)).values().stream().map(item -> item.get(0)).collect(Collectors.toList());
            TblServer server = new TblServer();
            server.setIps(CollUtil.newHashSet(CollUtil.distinct(flowDetailList.stream().map(FfsafeFlowDetail::getDip).collect(Collectors.toList()))));
            List<TblServer> serverList = serverService.selectTblServerList(server);
            flowDetailList.forEach(flowDetail -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("dip",flowDetail.getDip());
                jsonObject.put("sip",flowDetail.getSip());
                jsonObject.put("id",flowDetail.getId());
                if(CollUtil.isNotEmpty(serverList)){
                    serverList.stream().filter(item -> item.getIp().equals(flowDetail.getDip())).findFirst().ifPresent(matchServer -> jsonObject.put("serverName",matchServer.getAssetName()));
                }
                result.add(jsonObject);
            });
        }
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, result.size());
        if (startIndex >= result.size()) {
            resultObject.put("total", result.size());
            resultObject.put("rows", result);
            return resultObject;
        }
        List<JSONObject> jsonObjectList = result.subList(startIndex, endIndex);
        resultObject.put("total", result.size());
        resultObject.put("rows", jsonObjectList);
        return resultObject;
    }

    @Override
    public JSONObject getThreatenNameDetails(TblAttackAlarm tblAttackAlarm) {
        JSONObject resultObject = new JSONObject();
        List<JSONObject> result = new ArrayList<>();
        FfsafeFlowDetail query = new FfsafeFlowDetail();
        query.setSip(tblAttackAlarm.getAttackIp());
        query.setStartTime(tblAttackAlarm.getStartTime());
        query.setEndTime(tblAttackAlarm.getUpdateTime());
        List<FfsafeFlowDetail> flowDetailList = flowDetailService.selectList(query);
        if(CollUtil.isNotEmpty(flowDetailList)){
            List<List<FfsafeFlowDetail>> groupByField = CollUtil.groupByField(flowDetailList, "threatenName");
            groupByField.forEach(groupItem -> {
                FfsafeFlowDetail first = groupItem.get(0);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("sip",first.getSip());
                jsonObject.put("threatenName",first.getThreatenName());
                jsonObject.put("threatenType",first.getThreatenType());
                jsonObject.put("alarmLevel",first.getAlarmLevel());
                jsonObject.put("count",groupItem.size());
                result.add(jsonObject);
            });
        }
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, result.size());
        if (startIndex >= result.size()) {
            resultObject.put("total", result.size());
            resultObject.put("rows", result);
            return resultObject;
        }
        List<JSONObject> jsonObjectList = result.subList(startIndex, endIndex);
        resultObject.put("total", result.size());
        resultObject.put("rows", jsonObjectList);
        return resultObject;
    }

    /**
     * 获取数据类型
     * @return 攻击告警数据类型
     */
    @Override
    public DataTypeEnum getDataType() {
        return DataTypeEnum.ATTACK_ALARM;
    }

    /**
     * 处理MQ同步消息和确认消息
     * 完全参照TblThreatenAlarmServiceImpl的handle方法实现
     * @param message 同步消息
     * @return 处理是否成功
     */
    @Override
    public boolean handle(SyncMessage<TblAttackAlarm> message) {
        if(OperationTypeEnum.ADD_BLOCKING.equals(message.getOperationType())){
            JSONObject params = BeanUtil.toBean(message.getData(), JSONObject.class);
            addBlockIp(params);
            return true;
        }
        TblAttackAlarm attackAlarm = BeanUtil.toBean(message.getData(), TblAttackAlarm.class);
        // 服务中台处置
        if (Objects.equals(OperationTypeEnum.HANDLE, message.getOperationType())) {
            // 攻击者视角模块暂不支持处置逻辑，可以在此添加相关处理
            log.info("收到攻击告警处置消息，暂不处理: attackIp={}, id={}",
                    attackAlarm.getAttackIp(), attackAlarm.getId());
        } else {
            TblAttackAlarm alarmInDB = selectTblAttackAlarmById(attackAlarm.getId());
            if (alarmInDB != null) {
                TblAttackAlarm update = new TblAttackAlarm();
                update.setId(attackAlarm.getId());
                String syncStatus = "1"; //已同步
                update.setSynchronizationStatus(syncStatus);
                tblAttackAlarmMapper.updateTblAttackAlarm(update);
            }
        }
        return true;
    }

    /**
     * 处理插入操作
     */
    private boolean handleInsert(TblAttackAlarm attackAlarm) {
        try {
            int result = insertTblAttackAlarm(attackAlarm);
            log.info("插入攻击告警成功: id={}, result={}", attackAlarm.getId(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("插入攻击告警失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理更新操作
     */
    private boolean handleUpdate(TblAttackAlarm attackAlarm) {
        try {
            int result = updateTblAttackAlarm(attackAlarm);
            log.info("更新攻击告警成功: id={}, result={}", attackAlarm.getId(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("更新攻击告警失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理删除操作
     */
    private boolean handleDelete(TblAttackAlarm attackAlarm) {
        try {
            int result = deleteTblAttackAlarmById(attackAlarm.getId());
            log.info("删除攻击告警成功: id={}, result={}", attackAlarm.getId(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("删除攻击告警失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理插入或更新操作
     */
    private boolean handleInsertOrUpdate(TblAttackAlarm attackAlarm) {
        try {
            // 先尝试查询是否存在
            TblAttackAlarm existing = selectTblAttackAlarmById(attackAlarm.getId());
            if (existing != null) {
                // 存在则更新
                return handleUpdate(attackAlarm);
            } else {
                // 不存在则插入
                return handleInsert(attackAlarm);
            }
        } catch (Exception e) {
            log.error("处理插入或更新攻击告警失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 异步发送攻击告警同步消息
     * @param attackAlarm 攻击告警数据
     * @param operationType 操作类型
     */
    @Async
    protected void sendDataSyncMessageAsync(TblAttackAlarm attackAlarm, OperationTypeEnum operationType) {
        try {
            SyncMessage<TblAttackAlarm> message = new SyncMessage<>();
            message.setDataType(DataTypeEnum.ATTACK_ALARM);
            message.setOperationType(operationType);
            message.setData(attackAlarm);
            message.setTimestamp(System.currentTimeMillis());
            handleDataSyncSender.sendDataSync(message);
            log.info("发送攻击告警同步消息成功: attackIp={}, operationType={}",
                    attackAlarm.getAttackIp(), operationType);
        } catch (Exception e) {
            // 异步发送失败记录日志，不影响主流程
            log.error("发送攻击告警同步消息失败: attackIp={}, 错误: {}",
                    attackAlarm.getAttackIp(), e.getMessage(), e);
        }
    }

    /**
     * 处理同步状态更新
     * 当接收端成功处理同步消息后，会发送确认消息，此方法用于更新同步状态为已同步
     * 参照TblThreatenAlarmServiceImpl的handle方法实现
     * @param attackAlarm 攻击告警数据
     * @return 处理是否成功
     */
    private boolean handleSyncStatusUpdate(TblAttackAlarm attackAlarm) {
        try {
            log.info("处理攻击告警同步确认消息: attackIp={}, id={}",
                    attackAlarm.getAttackIp(), attackAlarm.getId());

            // 检查数据库中是否存在对应记录
            TblAttackAlarm alarmInDB = selectTblAttackAlarmById(attackAlarm.getId());
            if (alarmInDB != null) {
                // 创建更新对象，只更新同步状态
                TblAttackAlarm update = new TblAttackAlarm();
                update.setId(attackAlarm.getId());
                String syncStatus = "1"; // 已同步，使用数字状态值与其他模块保持一致
                update.setSynchronizationStatus(syncStatus);

                // 更新数据库中的同步状态
                tblAttackAlarmMapper.updateTblAttackAlarm(update);

                log.info("攻击告警同步状态更新成功: attackIp={}, id={}",
                        attackAlarm.getAttackIp(), attackAlarm.getId());
                return true;
            } else {
                log.warn("攻击告警同步状态更新失败，未找到对应记录: attackIp={}, id={}",
                        attackAlarm.getAttackIp(), attackAlarm.getId());
                return false;
            }
        } catch (Exception e) {
            log.error("处理攻击告警同步确认消息失败: attackIp={}, id={}, 错误: {}",
                    attackAlarm.getAttackIp(), attackAlarm.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 查询未同步的攻击者视角告警列表（过去7天）
     * @return 未同步的攻击者视角告警列表
     */
    @Override
    public List<TblAttackAlarm> selectNotSyncList() {
        DateTime nowDate = DateUtil.date();
        DateTime begin = DateUtil.beginOfDay(DateUtil.offsetDay(nowDate, -7)); // 获取一周前的日期
        return tblAttackAlarmMapper.selectNotSyncList(begin);
    }

    /**
     * 添加阻断IP
     * 参照TblThreatenAlarmServiceImpl的addBlockIp方法实现
     * @param params 阻断参数
     * @return 处理结果
     */
    @Override
    public AjaxResult addBlockIp(JSONObject params) {
        String resStr = ffSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.ADD_BLOCK_IP_URL, params, null);
        if(StrUtil.isNotBlank(resStr) && !"ok".equals(JSON.parseObject(resStr).getString("message"))){
            return AjaxResult.error(JSON.parseObject(resStr).getString("message"));
        }
        return AjaxResult.success();
    }
}
