<!--扣分详情-->
<template>
  <div class="points-detail">
<!--    <div class="points-detail-title">
      <el-button v-hasPermi="['system:ThreatDeductionStandard:edit']" type="primary" v-if="!editable" @click="enterEditMode">编辑</el-button>
      <el-button type="success" v-if="editable" @click="saveEdit">保存</el-button>
    </div>-->
    <table class="points-detail-table">
      <thead>
      <tr>
        <th colspan="5">九紫平台风险/威胁扣分标准</th>
      </tr>
      <tr>
        <th></th>
        <th></th>
        <th>总分值</th>
        <th>单项总分</th>
        <th>单次扣分<br />(*等级系数)</th>
      </tr>
      </thead>
      <tbody>
      <template v-for="(category, categoryIndex) in pointsData">
        <tr v-if="category.category" :key="`${categoryIndex}-header`">
          <td :rowspan="category.items.length + 1" style="text-align: center;">
            {{ category.category }}
          </td>
        </tr>
        <tr v-for="(item, itemIndex) in category.items" :key="`${categoryIndex}-${itemIndex}`">
          <td>
            <!-- 名称列不可编辑 -->
            <span>{{ item.type }}</span>
          </td>
          <!-- 可编辑的分数列 -->
          <td>
            <input type="number" v-model.number="item.totalScore" v-if="editable" />
            <span v-else>{{ item.totalScore }}</span>
          </td>
          <td>
<!--            <input type="number" v-model.number="item.itemScore" v-if="editable" />-->
            <span>{{ item.itemTotal }}</span>
          </td>
          <td>
            <input type="number" v-model.number="item.deductionPerEvent" v-if="editable" />
            <span v-else>{{ item.deductionPerEvent }}</span>
          </td>
        </tr>
        <tr v-if="categoryIndex < pointsData.length - 1" :key="`${categoryIndex}-spacer`"><td colspan="5"></td></tr>
      </template>
      <tr :key="'total-row'">
        <td colspan="2">合计</td>
        <td>{{ totalScore }}</td>
        <td>{{ itemTotalScore }}</td>
        <td></td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import { listThreatDeductionStandard ,updateThreatDeductionStandard } from "@/api/aqsoc/threat-deduction-standard/ThreatDeductionStandard";
export default {
  name: "PointsDetail",
  props: {
    editBtnVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pointsData: [],
      // editable: false, // 控制整个表格的编辑状态
      originalData: null // 用于保存原始数据
    };
  },
  computed: {
    totalScore() {
      return this.pointsData.reduce((acc, category) => {
        return acc + category.items.reduce((catAcc, item) => catAcc + item.totalScore, 0);
      }, 0).toFixed(1);
    },
    itemTotalScore() {
      return this.pointsData.reduce((acc, category) => {
        return acc + category.items.reduce((catAcc, item) => catAcc + item.itemTotal, 0);
      }, 0).toFixed(1);
    },
    editable: {
      get() {
        return this.editBtnVisible;
      },
      set(value) {
        this.$emit('update:editBtnVisible', value)
      }
    }
  },
  created() {
    this.ThreatDeductionStandard();
  },
  methods: {
    ThreatDeductionStandard() {
      listThreatDeductionStandard({}).then(response => {
        this.pointsData = response.rows;
      });
    },
    // 进入编辑模式
    enterEditMode() {
      // 保存原始数据以便取消编辑时恢复
      this.originalData = JSON.parse(JSON.stringify(this.pointsData));
      this.editable = true;
    },

    // 保存编辑
    saveEdit() {
      this.editable = false;
      // 这里可以添加保存到后端的逻辑
      console.log("保存数据:", this.pointsData);
      let pointsData = []
      this.pointsData.forEach( category => {
        if (category.items){
          category.items.forEach( item => {
            pointsData.push(item)
          })
        }
      })
      updateThreatDeductionStandard(pointsData).then(response => {
        this.$message.success("保存成功");
      })
      this.originalData = null; // 清除备份
    }
  }
};
</script>

<style scoped lang="scss">
.points-detail {
  width: 100%;
  overflow-x: auto;
  .points-detail-title {
    display: flex;
    justify-content: flex-end;
  }
}

.points-detail-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0 0 20px;

  th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
  }
  td {
    height: 48px;
    color: #333333;
  }

  thead {
    tr:first-child th {
      height: 48px;
      background-color: #0074cc;
      color: rgba(255,255,255,1);
      font-size: 26px;
    }

    tr:nth-child(2) th {
      background-color: #f0f0f0 ;
      color: #707170 ;
    }
  }

  tbody {
    input {
      width: 100%;
      text-align: center;
      border: 1px solid #409EFF;
      padding: 4px;
      border-radius: 4px;
    }
  }
}
</style>
