<!--工作台-->
<template>
  <div class="custom-container">
    <div class="current-box">
      <div class="firstPart">
        <div class="fistPart_1">
          <div
            v-for="(item, index) in items"
            :key="index"
            :class="['title_1', {'active': item === activeItem}]"
            @click="overviewDisposeClick(item)"
          >
            <img :src="require(`@/assets/images/workbench/${item.icon}`)" alt="">
            <div :class="['sum-title', item.colorClass]">
              <div>{{ item.label }}</div>
              <div>{{ item.value }}</div>
            </div>
          </div>
        </div>
        <div v-loading="loading" class="fistPart_1 table-content">
          <div class="tabs-content">
            <el-tabs v-model="activeName" @tab-click="getAffairData">
              <el-tab-pane :label="`威胁通报(${bulletinTotal || 0})`" name="first"></el-tab-pane>
            </el-tabs>
            <span class="more" @click="$router.push({path: '/safeManage/notice2'})">更多</span>
          </div>
          <el-table
            height="100%"
            :data="threatNotificationData"
            @row-click="threatNotificationRowClick"
            :cell-style="{border: 0 + 'px'}">
            <el-table-column
              label="通报名称"
              prop="workName"
            />
            <el-table-column
              label="严重程度"
              prop="severityLevel"
              :formatter="severityLevelFormatter"
            >
            </el-table-column>
            <el-table-column
              label="通报日期"
              prop="reportDate"/>
            <el-table-column
              label="计划完成"
              prop="expectCompleteTime"/>
            <template slot="empty">
              <el-empty description="暂无数据" :image-size="120"></el-empty>
            </template>
          </el-table>
          <pagination
            style="margin: 10px 10px 0"
            v-show="bulletinTotal > 0"
            :total="bulletinTotal"
            :pageSizes="[5, 10, 20, 30]"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getWaitListData"
          />
        </div>
        <div v-loading="affairLoading" class="fistPart_1 table-content">
          <div class="tabs-content" style="height: 39px">
            <el-tabs v-model="activeName1" @tab-click="getAffairData">
              <el-tab-pane v-for="(item,index) in tabPosition" :key="index" :name="item.name">
                <span slot="label">{{ `${item.label}(${item.sum || 0})` }}</span>
              </el-tab-pane>
            </el-tabs>
            <div style="width: 8%; height: 100%; margin-right: 0; align-content: center; text-align: center;"
                 class="more" @click="$router.push({path: '/safeManage/workBench/operateWorkRecord'})">更多
            </div>
          </div>
          <el-table
            height="100%"
            :data="affairData"
            @row-click="affairRowClick"
            :cell-style="{border: 0 + 'px'}">
            <el-table-column
              prop="workName"
              label="事务名称">
            </el-table-column>
            <el-table-column
              prop="workTitle"
              label="事务标题">
            </el-table-column>
            <el-table-column
              prop="workType"
              label="事务类型">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.work_type" :value="scope.row.workType"/>
              </template>
            </el-table-column>
            <el-table-column
              prop="fnodeName"
              label="事务节点">
            </el-table-column>
            <el-table-column
              prop="fflowstate"
              label="事务状态">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.work_flow_state" :value="scope.row.fflowstate"/>
              </template>
            </el-table-column>
            <el-table-column
              prop="createTime"
              fixed="right"
              label="发起时间">
            </el-table-column>
            <template slot="empty">
              <el-empty description="暂无数据" :image-size="120"></el-empty>
            </template>
          </el-table>
          <pagination
            style="margin: 10px 10px 0"
            v-show="affairTotal > 0"
            :total="affairTotal"
            :pageSizes="[5, 10, 20, 30]"
            :page.sync="queryAffairParams.pageNum"
            :limit.sync="queryAffairParams.pageSize"
            @pagination="getAffairData"
          />
        </div>
      </div>
      <div class="firstPart1">
        <schedule></schedule>
      </div>
      <div class="firstPart1">
        <!-- 通知公告 -->
        <div class="firstPart_2" style="height: 50%">
          <div class="firstPart_2_1">
            <span class="header-title">通知公告</span>
            <span
              class="more"
              v-has-permi="['system:notice:list']"
              @click="noticeListDialogVisible = true">更多</span>
            <div style="height: calc(100% - 20px)">
              <el-table
                height="100%"
                :v-loading="noticeLoading"
                :data="tableData"
                :show-header="false"
                :row-class-name="tableRowClassName"
                @row-click="tableRowClick"
                :cell-style="{border:0 + 'px', color: '#333333', fontSize: 14 + 'px', cursor: 'pointer', paddingLeft: 0 + 'px' }">
                <el-table-column
                  class-name="column_s"
                  prop="noticeTitle"
                  label="标题">
                  <template slot-scope="scope">
                    <!--                    <span v-text="safeNoticeTitle(scope.row)"></span>-->
                    <span slot="reference">{{
                        `${scope.row.noticeType === '1' ? '【通知通告】' : '【通报公告】'}${safeNoticeTitle(scope.row)}`
                      }}</span>
                  </template>

                </el-table-column>
                <el-table-column
                  align="right"
                  prop="createTime"
                  width="150"
                  label="日期"
                >
                  <template slot-scope="scope">
                    <span>{{
                        parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}")
                      }}</span>
                  </template>
                </el-table-column>
                <template slot="empty">
                  <el-empty description="暂无数据" :image-size="120"></el-empty>
                </template>
              </el-table>
            </div>
          </div>
        </div>
        <div class="firstPart_2">
          <span class="header-title">快捷入口</span>
          <span class="more"
                v-hasPermi="['safe:home:setQuickAccess']"
                v-if="isExists && roles.includes('admin')"
                @click="showEntranceDialog">
            <i style="font-size: 20px" class="el-icon-setting"/>
          </span>
          <div class="entrance-content" v-loading="entranceLoading" v-if="entranceData.length > 0">
            <div style="display: flex; justify-content: start; gap: 5px">
              <div
                class="entrance-item"
                v-for="(item, index) in entranceData.slice(0, 4)"
                @click="clickSkipPage(item.path)"
                :key="index">
                <span class="bottom"><img :src="item.icon" alt=""/></span>
                <span>{{ item.title }}</span>
              </div>
            </div>
            <div style="display: flex; justify-content: start; gap: 5px">
              <div
                class="entrance-item"
                v-for="(item, index) in entranceData.slice(4, 8)"
                @click="clickSkipPage(item.path)"
                :key="index">
                <span class="bottom"><img :src="item.icon" alt=""/></span>
                <span>{{ item.title }}</span>
              </div>
            </div>
          </div>
          <div class="entrance-content" v-else>
            <el-empty description="暂无数据" :image-size="120"></el-empty>
          </div>
          <PaginationDots
            v-if="totalDots > 0"
            :totalDots="totalDots"
            :initialIndex="quickEntryQueryParams.pageNum - 1"
            @update:index="handlePageIndexChange"
          />
        </div>
      </div>
    </div>
    <DraggableDialog
      @confirm="getQuickAccessData"
      :entranceDialogVisible.sync="entranceDialogVisible"/>

    <!--通知公告详情 -->
    <NoticeDetailsDialog
      :is-link="true"
      @openAffair="openAffair"
      @refresh="getNoticeList"
      :notice-details="noticeDetails"
      :notice-detail-visible.sync="noticeDialogVisible"/>

    <!--通知公告弹窗 -->
    <NoticeListDialog :noticeListDialogVisible.sync="noticeListDialogVisible"/>

    <!-- 循环创建多个弹窗 -->
    <template v-for="(popup, index) in popupList">
      <el-dialog
        class="notice-dialog"
        :key="`dialog-${index}-${dialogKey}`"
        :visible.sync="popupVisibleList[index]"
        width="40%">
        <div slot="title">
          <div
            style="font-size: 16px;
            display: flex;
            color: #353535;">
            <img src="@/assets/images/workbench/antFill-notification.png" alt=""/>
            <span style="margin-left: 6px;">公告详情</span>
          </div>
        </div>
        <el-form class="notice-form" label-position="top">
          <el-form-item label="公告标题">
            <span style="color: rgba(16,16,16,1);font-size: 14px;">{{ popup.noticeTitle }}</span>
          </el-form-item>
          <el-form-item label="公告内容">
            <div style="color: rgba(16,16,16,1);font-size: 14px;">{{ popup.noticeContent }}</div>
          </el-form-item>
          <el-form-item label="关联入口" v-if="popup.entrances && popup.entrances.length > 0">
            <div v-for="(item, index) in popup.entrances" :key="index" class="custom-div-item">
              <div @click="linkDetails(item)">{{ item.entranceName }}</div>
            </div>
          </el-form-item>
        </el-form>
      </el-dialog>
    </template>

    <FlowBox v-if="flowVisible" ref="FlowBox" @close="flowBoxClose"/>
  </div>
</template>

<script>
import {getNoticeDetails, homelistNotice} from "@/api/system/notice";
import {parseTime} from "@/utils/ruoyi";
import schedule from './schedule.vue';
import DraggableDialog from './DraggableDialog.vue';
import NoticeListDialog from "./NoticeListDialog.vue"
import SafetyMatterList from "@/views/homePage/component/SafetyMatterList.vue";
import FlowTodoList from "@/views/homePage/component/FlowTodoList.vue";
import NoticeDialog from "@/views/system/notice/NoticeDialog.vue";
import FlowBox from "@/views/zeroCode/workFlow/components/FlowBox.vue";
import {waitList} from "@/api/tool/work";
import {listOperateWorkRecord} from "@/api/operateWork/operateWorkRecord";
import {getCheckExists, getCountTitleData, getPopupList, getQuickAccessList} from "@/api/homePage/indexDeveloping";
import PaginationDots from "@/views/homePage/component/PaginationDots.vue";
import NoticeDetailsDialog from "@/views/system/notice/NoticeDetailsDialog.vue";
import {getShowTips} from "@/api/system/loginTips";
import {FlowEngineInfo} from "@/api/lowCode/FlowEngine";
import {mapGetters} from "vuex";

export default {
  name: "workbench",
  components: {
    NoticeDetailsDialog,
    PaginationDots,
    FlowBox, NoticeDialog, FlowTodoList, SafetyMatterList, schedule, DraggableDialog, NoticeListDialog
  },
  dicts: ["loophole_category", "threaten_type", "sys_notice_type", "work_flow_state", "work_type","work_order_severity_level"],
  data() {
    return {
      tipsDialogVisible: false,
      tips: [],
      activeName: 'first',
      activeName1: '1',
      noticeLoading: false,
      tableData: [],
      noticeList: [],
      loading: true,
      affairLoading: true,
      threatNotificationData: [],
      flowLaunch: 0, // 我发起
      flowTodo: 0, // 待办
      flowCirculate: 0, // 抄送
      flowDone: 0, // 已办
      queryFlowStatus: 0,
      items: [
        {
          type: 'todo',
          label: '我的待办',
          icon: 'agenda.png',
          colorClass: 'orange',
          value: 0,
          HandleUser: null,
          affairsStatus: null,
          threatNotificationStatus: null,
          classStats: [],
        },
        {
          type: 'done',
          label: '我的已办',
          icon: 'have.png',
          colorClass: 'green',
          value: 0,
          HandleUser: null,
          affairsStatus: null,
          threatNotificationStatus: null,
          classStats: [],
        },
        {
          type: 'launch',
          label: '我的发起',
          icon: 'session.png',
          colorClass: 'blue',
          value: 0,
          HandleUser: null,
          affairsStatus: null,
          threatNotificationStatus: null,
          classStats: [],
        },
        {
          type: 'circulate',
          label: '抄送我的',
          icon: 'make.png',
          colorClass: 'gray',
          value: 0,
          HandleUser: null,
          affairsStatus: null,
          threatNotificationStatus: null,
          classStats: [],
        },
      ],
      tabPosition: [],
      entranceLoading: false,
      entranceData: [],
      queryParam: {
        str: 'flowLaunch',
        category: '',
      },
      queryParams: {
        pageNum: 1,
        pageSize: 5,
      },
      quickEntryQueryParams: {
        pageNum: 1,
        pageSize: 8,
      },
      totalDots: 0,
      affairData: [],
      queryAffairParams: {
        pageNum: 1,
        pageSize: 5,
      },
      affairTotal: 0,
      bulletinTotal: 0,
      noticeDialogVisible: false,
      entranceDialogVisible: false,
      noticeListDialogVisible: false,
      flowVisible: false,
      noticeDetails: {},
      searchEventTypeOptions: [
        {
          dictLabel: 'IP漏洞事件',
          dictValue: 'IP漏洞事件',
          /* dictType: 'loophole_category',
          children: [] */
        },
        {
          dictLabel: 'Web漏洞事件',
          dictValue: 'Web漏洞事件',
          /* dictType: 'loophole_category',
          children: [] */
        },
        {
          dictLabel: '威胁事件',
          dictValue: '威胁事件',
          /* dictType: 'threaten_alarm_type',
          children: [] */
        },
        {
          dictLabel: '弱口令事件',
          dictValue: '弱口令事件',
        },
        {
          dictLabel: '其它',
          dictValue: '其它',
        },
      ],
      _query: {},
      waitQuery: {},
      activeItem: null, // 新增选中项变量
      currentFlowData: {},
      dialogKey: 0,
      popupList: [],
      currentPopup: {},
      popupVisibleList: [],
      isExists: false, // 快捷入口是否存在数据
    }
  },
  created() {
    this.initData();
  },
  computed: {
    ...mapGetters([
      'roles'
    ]),
    severityLevelOptions(){
      return this.dict.type.work_order_severity_level;
    },
  },
  watch: {
    items: {
      handler(newValue, oldValue) {
        if (newValue.length > 0) {
          this.overviewDisposeClick(newValue[0])
        }
      },
      deep: true,
    }
  },
  methods: {
    parseTime,
    async initData() {
      await this.getPopupListData();
      await this.getNoticeList();
      await this.getCountTitleData()
      await this.getQuickAccessData();
    },

    // 点击顶部卡片,切换不同数据
    overviewDisposeClick(item) {
      this.activeItem = item; // 更新选中项
      if (item.type === 'todo') {
        this._query = {
          queryFlowState: item.affairsStatus,
        }
        this.waitQuery = {
          currentUserId: item.threatNotificationStatus,
        }
      } else if (item.type === 'done') {
        this._query = {
          queryFlowState: item.affairsStatus,
        }
        this.waitQuery = {
          flowHandleUser: item.HandleUser,
          queryState: '4',
          onlySelf: true
        }
      } else if (item.type === 'launch') {
        this._query = {
          createByName: item.affairsStatus
        }
        this.waitQuery = {
          remark5: item.threatNotificationStatus
        }
      } else if (item.type === 'circulate') {
        this._query = {}
        this.waitQuery = {
          fHandleUser: item.HandleUser,
          findCarbonCopyStatus: item.threatNotificationStatus
        }
      }
      this.bulletinTotal = item.threatNotificationNum;

      this.loading = true;
      waitList({
        ...this.waitQuery,
        ...this.queryParams
      }).then((res) => {
        this.loading = false;
        if (res.code === 200) {
          this.bulletinTotal = res.total;
          this.threatNotificationData = res.rows;
        }
      })
      // 事务列表无抄送状态
      if (item.type !== 'circulate') {
        this.tabPosition = item.classStats.map((item) => {
          return {
            label: item.workClassName,
            name: item.workClass.toString(),
            sum: item.total
          };
        });
        this.affairLoading = true;
        listOperateWorkRecord({
          workClass: this.activeName1,
          ...this._query,
          ...this.queryAffairParams
        }).then(response => {
          this.affairLoading = false;
          if (response.code === 200) {
            this.affairData = response.rows;
            this.affairTotal = response.total;
          }
        });
      }
      if (item.type === 'circulate') {
        this.tabPosition = []
        this.affairData = [];
        this.affairTotal = 0;
      }
    },

    // 获取威胁通报列表
    async getWaitListData() {
      this.loading = true;
      waitList({
        ...this.waitQuery,
        ...this.queryParams
      }).then((res) => {
        this.loading = false;
        if (res.code === 200) {
          this.bulletinTotal = res.total;
          this.threatNotificationData = res.rows;
        }
      })
    },

    // 获取事务列表数据
    getAffairData() {
      this.affairLoading = true;
      this.queryAffairParams.workClass = this.activeName1;
      listOperateWorkRecord({
        ...this._query,
        ...this.queryAffairParams
      }).then(response => {
        this.affairLoading = false;
        if (response.code === 200) {
          this.affairData = response.rows;
          this.affairTotal = response.total;
        }
      });
    },

    // 获取统计数据
    getCountTitleData() {
      getCountTitleData().then(response => {
        if (response.code === 200) {
          this.items.forEach(item => {
            if (item.type === 'todo') {
              item.value = response.data.myAgent.total;
              item.affairsStatus = response.data.myAgent.affairsStatus;
              item.HandleUser = response.data.myAgent.HandleUser;
              item.classStats = response.data.myAgent.classStats;
              item.threatNotificationNum = response.data.myAgent.threatNotificationNum;
              item.threatNotificationStatus = response.data.myAgent.threatNotificationStatus;
            } else if (item.type === 'done') {
              item.value = response.data.myDone.total;
              item.HandleUser = response.data.myDone.HandleUser;
              item.affairsStatus = response.data.myDone.affairsStatus;
              item.classStats = response.data.myDone.classStats;
              item.threatNotificationNum = response.data.myAgent.threatNotificationNum;
              item.threatNotificationStatus = response.data.myDone.threatNotificationStatus;
            } else if (item.type === 'launch') {
              item.value = response.data.myLaunch.total;
              item.affairsStatus = response.data.myLaunch.affairsStatus;
              item.HandleUser = response.data.myLaunch.HandleUser;
              item.classStats = response.data.myLaunch.classStats;
              item.threatNotificationNum = response.data.myAgent.threatNotificationNum;
              item.threatNotificationStatus = response.data.myLaunch.threatNotificationStatus;
            } else if (item.type === 'circulate') {
              item.value = response.data.carbonCopyMy.total;
              item.affairsStatus = response.data.carbonCopyMy.affairsStatus;
              item.HandleUser = response.data.carbonCopyMy.HandleUser;
              item.classStats = response.data.carbonCopyMy.classStats;
              item.threatNotificationNum = response.data.myAgent.threatNotificationNum;
              item.threatNotificationStatus = response.data.carbonCopyMy.threatNotificationStatus;
            }
          });
        }
      })
    },


    // 通知公告表格行样式
    tableRowClassName({row, rowIndex}) {
      if (row.isTop === '1' || row.isTop === '是') {
        return 'highlight-row';
      }
      return '';
    },

    // 获取弹窗通知列表
    getPopupListData() {
      getPopupList().then(res => {
        if (res.code === 200) {
          if (res.data.length > 0) {
            this.popupList = res.data.reverse();

            // 初始化弹窗可见性数组
            this.popupVisibleList = new Array(this.popupList.length).fill(true);

            // 强制更新所有弹窗
            this.dialogKey = Date.now();
          }
        }
      });
    },

    // 获取通知公告列表
    getNoticeList() {
      this.noticeLoading = true;
      homelistNotice({
        pageNum: 1,
        pageSize: 9,
        includeRead: false,
      }).then((res) => {
        if (res.code === 200) {
          this.tableData = res.rows;
        }
      }).finally(() => {
        this.noticeLoading = false;
      })
    },

    // 获取快捷入口列表
    getQuickAccessData() {
      this.entranceLoading = true;
      getCheckExists().then(res => {
        if (res.code === 200) {
          this.isExists = res.data;
          if (this.isExists) {
            getQuickAccessList(this.quickEntryQueryParams).then(res => {
              this.entranceData = res.rows.map(item => {
                return {
                  title: item.shortcutEntrance.entranceName,
                  icon: item.shortcutEntrance.iconUrl,
                  path: item.menuPath
                }
              });
              this.totalDots = Math.ceil(res.total / this.quickEntryQueryParams.pageSize)
            })
          }
        }
      }).finally(() => {
        this.entranceLoading = false;
      })
    },

    // 跳转菜单
    clickSkipPage(path) {
      if (path) this.$router.push({path: path});
      this.tipsDialogVisible = false;
    },

    // 表格行点击(查看通知通报详情)
    tableRowClick(row, column, event) {
      getNoticeDetails(row.noticeId).then(response => {
        this.noticeDetails = response.data;
        this.noticeDialogVisible = true;
      });
    },

    showEntranceDialog() {
      this.entranceDialogVisible = true;
    },

    handlePageIndexChange(index) {
      this.quickEntryQueryParams.pageNum = index + 1;
      this.getQuickAccessData();
    },

    safeNoticeTitle(row) {
      // 防止XSS攻击，转义HTML
      return row.noticeTitle ? row.noticeTitle.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;') : '';
    },

    severityLevelFormatter(row, column, cellValue, index) {
      let name = '';
      let match = this.severityLevelOptions.find(item => item.value === cellValue);
      if(match){
        name = match.label;
      }
      return name;
    },
    loopEventTypeFormatter(array, value) {
      for (const item of array) {
        if (item.dictValue === value) {
          return item.dictLabel;
        }
        if (item.children && item.children.length > 0) {
          const label = this.loopEventTypeFormatter(item.children, value);
          if (label) {
            return label;
          }
        }
      }
      return value; // 如果未找到，返回 null
    },

    // 威胁通报列表点击事件
    threatNotificationRowClick(row, column, event) {
      let data = {};
      if (row.handleState === row.flowState && row.handleState !== 4 && this.hasHandlePrem(row)) {
        data = {
          id: row.prodId || "",
          formType: 1,
          opType: row.flowState ? (row.flowState === "-1" ? "-1" : 1) : "-1",
          status: null,
          row: row,
          activeTabs: null,
          isWork: true
        };
      } else {
        data = {
          id: row.prodId,
          formType: 1,
          opType: -2,
          status: null,
          readOnly: true,
          row: row,
          activeTabs: null
        };
      }
      this.$nextTick(() => {
        this.openFlowDetail(data);
      });
    },

    // 事务列表点击事件
    affairRowClick(row, column, event) {
      let data = {};
      // 判断是已办或发起，如果是已办和发起，流程显示详情，如果是待办，则流程显示处理
      if (this.activeItem.type === 'done' || this.activeItem.type === 'launch') {
        data = {
          id: row.fflowtaskid,
          flowId: row.fflowid,
          opType: 0,
          status: '',
          parentId: '0',
        };
      } else {
        data = {
          id: row.fflowtaskid,
          flowId: row.fflowid,
          opType: row.fflowstate == null ? 0 : row.fflowstate === 0 ? -1 : row.fflowstate === -1 ? -1 : 1,
          status: '',
          parentId: '0',
        };
      }
      this.$nextTick(() => {
        this.openFlowDetail(data);
      });
    },

    linkDetails(item) {
      if (item.entranceType === 'link') {
        let url = item.entranceUrl;

        if (!url.startsWith('http')) {
          url = 'https://' + url;
        }

        const a = document.createElement('a');
        a.href = url;
        a.target = '_blank';
        a.rel = 'noopener noreferrer';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      } else if (item.entranceType === 'affair') {
        this.openAffair(item);
      } else {
        window.open(item.entranceUrl, '_self');
      }
    },

    // 通知详情点击事务
    openAffair(affairData) {
      this.popupVisibleList = this.popupVisibleList.map(item => false);
      this.noticeDialogVisible = false;
      let data = {
        id: "",
        formType: 1,
        opType: '-1',
        status: null,
        row: {...affairData.operateWork}
      }
      data.row.workId = data.row.entranceId;
      this.currentFlowData = data;
      data.row.id = null;
      FlowEngineInfo(affairData.operateWork.flowTemplateId).then(res => {
        if(res.data && res.data.flowTemplateJson){
          let data = JSON.parse(res.data.flowTemplateJson);
          if(!data[0].flowId){
            this.$message.error('该流程模板异常,请重新选择');
          }else {
            this.currentFlowData.flowId = data[0].flowId;
            this.flowVisible = true;
            this.$nextTick(() => {
              this.$refs.FlowBox.init(this.currentFlowData);
            });
          }
        }
      })
    },

    // 判断是否有处理权限
    hasHandlePrem(row) {
      let userId = sessionStorage.getItem('userId');
      if (!userId) {
        return false;
      }
      userId = parseInt(userId.toString());
      if (row.currentHandleUserList && row.currentHandleUserList.length > 0) {
        return row.currentHandleUserList.find(item => item.userId === userId);
      }
      return false;
    },

    // 打开流程详情
    openFlowDetail(data) {
      this.flowVisible = true;
      this.$nextTick(() => {
        this.$refs.FlowBox.init(data);
      });
    },
    flowBoxClose(val) {
      this.flowVisible = false
      if (val) {
        this.getWaitListData();
        this.getAffairData();
      }
    }
  }
}
</script>

<style scoped lang="scss">
@font-face {
  font-family: electronicFont;
  src: url(../../../assets/fonts/DS-DIGI.ttf);
}

.orange {
  color: #FFA500;
}

.green {
  color: #00CC00;
}

.blue {
  color: #4382FD;
}

.gray {
  color: #808080;
}

.current-box {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 16px;
}

.notice-dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px 30px 42px;
  }
}

.notice-form {
  ::v-deep .el-form-item__label {
    color: #2D529F;
    font-size: 14px;
    font-weight: bold;
  }
  ::v-deep .el-form-item__content {
    display: flex;
    .custom-div-item {
      display: flex;
      /*flex: 1 1 calc(20% - 5px);*/
      div {
        cursor: pointer;
        color: #4382FD;
        margin-right: 10px;
        padding: 3px 15px;
        line-height: 20px;
        background-color: rgba(243,247,247,1);
        color: rgba(108,108,108,1);
        font-size: 14px;
        text-align: center;
        border: 1px solid rgba(187,187,187,1);
      }
    }
  }
}


.firstPart {
  width: calc((100% - 30px) / 2);
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .fistPart_1 {
    width: 100%;
    height: 90px;
    display: flex;
    gap: 16px;

    .title_1 {
      width: 25%;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      background: #fff;
      cursor: pointer;

      &:hover {
        background: #f3f8fe;
      }

      &.active {
        background: #f3f8fe;
      }

      img {
        width: 64px;
        height: 64px;
      }

      .sum-title {
        text-align: center;
        line-height: 35px;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  .table-content {
    width: 100%;
    flex: 1;
    gap: 0;
    display: flex;
    padding-bottom: 10px;
    flex-direction: column;
    background: #ffffff;

    .tabs-content {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // ElementUI tabs 样式穿透
      ::v-deep .el-tabs {
        width: 92%;
        height: 100%;

        &__header {
          height: 100%;
          overflow-y: hidden;
          overflow-x: auto;
          flex: 1;
          margin: 0;
          /*padding: 0 16px;*/
          background: #FFFFFF;
        }

        &__nav-wrap::after {
          height: 1px;
        }

        &--top {
          & .el-tabs__item.is-top {
            padding: 0 20px;
            margin-right: 8px;
          }
        }

        &__active-bar {
          border: none;
          display: none;
        }

        &__item.is-active {
          color: #1890ff;
          border-bottom: 2px solid #1890ff;
        }

        &__content {
          display: none;
        }
      }
    }

    ::v-deep.el-table {
      .el-table__row {
        &:hover {
          cursor: pointer;
        }
      }

      .el-table__fixed-header-wrapper {
        height: 30px !important;
      }

      .cell {
        padding-left: 16px !important;
      }

      th {
        height: 30px !important;
        color: #707170 !important;
        font-size: 14px !important;
        font-weight: normal !important;
        background-color: #f0f0f0 !important;
        border-bottom: 1px solid #f0f0f0 !important;

        .el-table__cell > .cell {
          padding-left: 16px
        }
      }

      tr {
        color: #333;
        font-size: 14px;
        height: 30px !important;
      }
    }

    ::v-deep.el-table::before, .el-table--group::after, .el-table--border::after {
      background-color: #ffffff !important;
    }

    ::v-deep.el-table__fixed-right::before {
      display: none !important;
    }
  }

  .header-title {
    font-size: 16px;
    color: #333333;
    padding: 5px 0;
    font-weight: bold;
  }
}

.part-box {
  flex: 1;
}

.firstPart1 {
  display: flex;
  flex-direction: column;
  width: calc((100% - 30px) / 4);
  gap: 16px;

  .firstPart_2 {
    width: 100%;
    flex: 1;
    padding: 16px;
    background: #fff;

    .firstPart_2_1 {
      height: 100%;

      ::v-deep .el-table .highlight-row {
        td {
          color: #e5404f !important;
        }
      }
      ::v-deep .el-table--group::after, .el-table--border::after {
        background-color: #ffffff !important;
      }
      .el-table::before {
        height: 0;
      }

      ::v-deep.el-table .cell {
        padding-left: 0 !important;
      }
    }
  }

  .schedule {
    width: 100%;
    height: 100%;
    background: #fff;
  }
}

.title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  height: 100px;
  padding: 0 0 0 60px;

  span {
    margin-left: 15px;
    font-size: 36px;
    color: #333333;
    font-weight: 700;
    font-family: electronicFont;
  }
}

.custom-column {
  ::v-deep .el-descriptions-item__content {
    display: flex;
    flex-wrap: wrap;
  }
}

.more {
  float: right;
  cursor: pointer;
  margin-right: 20px;
  font-size: 14px;
  color: #4382FD;
  flex-shrink: 0;
}

.quickEntrance {
  display: flex;
  flex-direction: column;
}

.entrance-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  height: calc(100% - 40px);
  padding: 20px 0;

  .entrance-item {
    width: calc(100% / 4);
    height: 125px;
    display: flex;
    cursor: pointer;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .bottom {
      width: 88px;
      height: 88px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      margin-bottom: 15px;
      background: #e4f4ff;

      img {
        width: 66px;
        height: 66px;
      }
    }
  }
}
</style>
