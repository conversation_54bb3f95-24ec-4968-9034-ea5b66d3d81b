<template>
  <div>
  <el-select
    ref="select"
    filterable
    :filter-method="filterMethod"
    @visible-change="clearFilter"
    v-model="valueLabel"
    :disabled="disabled"
    :clearable="clearable"
    @clear="handleClear"
    :size="size"
    v-bind="$attrs"
    v-on="$listeners"
    :popper-append-to-body="false"
  >
    <el-option :value="valueLabel" :label="valueLabel" class="options">
      <el-tree
        id="tree-option"
        ref="selectTree"
        :data="options"
        :props="props"
        :accordion="accordion"
        :node-key="props.value"
        :filter-node-method="filterNode"
        :default-expanded-keys="defaultExpandedKey"
        :default-expand-all="props.isExpandAll"
        :expand-on-click-node="false"
        :check-on-click-node="true"
        @node-click="handleNodeClick"
      />
    </el-option>
  </el-select>
  </div>
</template>

<script>
import { waitForValue } from "@/utils/ruoyi";
import { getValFromObject } from "@/utils";

export default {
  name: "SelectTree",
  props: {
    // 初始值
    value: { type: [String, Number], default: null },
    // 配置选项
    props: {
      type: Object,
      default: () => ({
        // 值字段名
        value: "id",
        // 标签字段名
        label: "label",
        // 子级字段名
        children: "children",
        disabled: "disabled",
        isExpandAll: true
      }),
    },
    // 选项列表数据(树形结构的对象数组)
    options: { type: Array, default: () => [] },
    // 是否禁用
    disabled: { type: Boolean, default: false },
    // 是否可以清空选项
    clearable: { type: Boolean, default: false },
    // 是否每次只打开一个同级树节点展开
    accordion: { type: Boolean, default: false },
    // 输入框尺寸，可选值：medium/small/mini
    size: { type: String, default: "small" },
    /* 是否是部门下拉框 */
    isDept:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      valueId: null,
      valueLabel: null,
      defaultExpandedKey: [],
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.valueId = null;
        this.valueLabel = "";
        this.defaultExpandedKey = [];
        this.valueId = newVal;
        this.init();
      },
      deep: true,
      immediate: true,
    },

  },
  mounted() {
    this.$nextTick(() => {
      this.valueId = this.value;
      // this.init();
    });
  },
  methods: {

    /** 初始化 */
    async init() {
      if (this.valueId && this.valueId != null) {
        let that = this;
        // 初始化显示
        // this.valueLabel = this.$refs.selectTree.getNode(this.valueId).data[this.props.label];
        await waitForValue(() => {
          let sele;
          if ((sele = getValFromObject("selectTree", that.$refs))) {
            return sele.getNode(that.valueId);
          }
          return null;
        }).then((node) => {
          that.valueLabel = node.data[that.props.label];
        });
        // 设置默认选中
        this.$refs.selectTree.setCurrentKey(this.valueId);
        // 设置默认展开
        this.defaultExpandedKey = [this.valueId];
      }
      this.initScroll();
    },
    /** 初始化滚动条 */
    initScroll() {
      this.$nextTick(() => {
        const scrollWrap = document.querySelectorAll(
          ".el-scrollbar .el-select-dropdown__wrap"
        )[0];
        const scrollBar = document.querySelectorAll(
          ".el-scrollbar .el-scrollbar__bar"
        );
        scrollWrap.style.cssText =
          "margin: 0px; max-height: none; overflow: hidden;";
        scrollBar.forEach((ele) => (ele.style.width = 0));
      });
    },
    /** 切换选项 */
    handleNodeClick(node) {
      // 让下拉框失去焦点事件
      this.$refs.select.blur();
      this.valueId = node[this.props.value];
      this.valueLabel = node[this.props.label];
      this.defaultExpandedKey = [];
      this.$emit("input", this.valueId);
    },
    /** 清空选中 */
    handleClear() {
      this.valueId = null;
      this.valueLabel = "";
      this.defaultExpandedKey = [];
      this.clearSelected();
      this.$emit("input", null);
    },
    /** 清空选中样式 */
    clearSelected() {
      const allNode = document.querySelectorAll("#tree-option .el-tree-node");
      allNode.forEach((element) => element.classList.remove("is-current"));
    },

    /* 下拉框出现或者隐藏时清空过滤 */
    clearFilter(callback){
      this.$emit('visible-change', callback)
      let vlaue='';
      this.filterMethod(vlaue);
    },

    // 下拉框搜索过滤
    filterMethod(value) {
      if (this.isDept){
        this.$refs.selectTree.filter(value);
      }
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

  },
};
</script>

<style lang="scss" scoped>
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: 100%;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

.el-tree ::v-deep .el-tree-node__content {
  height: auto;
  padding: 0 20px;
}

.el-tree-node__label {
  font-weight: normal;
}

.el-tree ::v-deep .is-current > .el-tree-node__content {
  background: #e3f2fd;
}

.el-tree ::v-deep .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}

.el-tree ::v-deep .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}
</style>
