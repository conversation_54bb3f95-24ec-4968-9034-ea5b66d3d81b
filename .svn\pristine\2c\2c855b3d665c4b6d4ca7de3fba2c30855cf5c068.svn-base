<!--资产字段配置-->
<template>
  <div class="asset-field-container">
    <div class="asset-field-title">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="业务系统" name="1"></el-tab-pane>
        <el-tab-pane label="主机" name="2"></el-tab-pane>
        <el-tab-pane label="终端" name="3"></el-tab-pane>
        <el-tab-pane label="网络设备" name="4"></el-tab-pane>
        <el-tab-pane label="安全设备" name="5"></el-tab-pane>
        <el-tab-pane label="区域设备" name="6"></el-tab-pane>
      </el-tabs>
      <div class="btn">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </div>
    <div class="asset-field-content">
      <div class="left-board">
        <div v-for="(item, index) in fieldData" :key="index" class="field-packet">
          <div class="board-title" style="padding: 0">
            <div class="title"><img src="@/assets/images/antOutline-form.png" alt=""/>{{ item.formName }}</div>
            <div class="num">
              {{ `${computedFieldData[index].selectedNum}/${(item.fieldsItems || []).length}` }}
            </div>
          </div>
          <draggable
            v-model="item.fieldsItems"
            item-key="index"
            @change="onChange"
          >
            <div class="board-content" v-for="(ele, eleIndex) in item.fieldsItems" :key="eleIndex">
              <div class="field-item" style="cursor: move">
                <img src="@/assets/images/iconPark-drag.png" alt=""/>
                <div :style="!ele.isShow ? { backgroundColor: '#F5F5F5' } : ''">{{ ele.fieldName }}</div>
                <i
                  class="el-icon-edit"
                  @click="handleEdit(ele, index, eleIndex)"
                  style="cursor: pointer"
                />
                <el-checkbox
                  style="margin-left: 12px"
                  v-model="ele.isShow"
                  @change="handleVisibilityChange(ele)"
                />
              </div>
            </div>
          </draggable>
        </div>
      </div>
<!--      <div class="center-board"></div>-->
      <div class="right-board">
        <div class="board-title">
          <div class="title"><img src="@/assets/images/iconPark-config.png" alt=""/>字段属性配置</div>
        </div>
        <div class="board-content" style="padding: 24px 12px; width: 300px;">
          <el-form ref="fieldForm" :model="currentField" :disabled="editDisabled" label-width="80px">
            <el-form-item label="字段名称">
              <el-input clearable v-model="currentField.fieldName"></el-input>
            </el-form-item>
            <el-form-item label="是否必填">
              <el-switch v-model="currentField.required"></el-switch>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import draggable from "vuedraggable";
import {listAssetFields, updateAssetFields} from '@/api/asset/assetFields'

export default {
  name: "index",
  components: {draggable},
  data() {
    return {
      activeName: "1",
      form: {},
      editDisabled: true,
      currentField: {}, // 当前编辑的字段
      originalFieldData: [], // 原始数据备份
      fieldData: [
        {
          packetName: "基本信息",
          total: 17,
          checked: 16,
          packetChildren: [
            {
              name: "资产编号",
              required: true
            },
            {
              name: "资产名称",
              required: true
            },
            {
              name: "资产类型",
              required: true
            },
            {
              name: "资产状态",
              required: true
            },
            {
              name: "资产描述",
              required: true
            },
            {
              name: "资产负责人",
              required: true
            }
          ]
        }
      ]
    }
  },
  created() {
    this.init();
  },
  watch: {
    // 当切换资产类型时重置当前编辑字段
    activeName() {
      this.currentField = {};
    },
    currentField: {
      handler(newVal) {
        // 当 currentField 变化时同步到原始数据
        if (newVal && newVal.groupIndex !== undefined && newVal.fieldIndex !== undefined) {
          this.syncFieldToSource(newVal);
        }
      },
      deep: true
    }
  },
  computed: {
    computedFieldData() {
      return this.fieldData.map(item => ({
        ...item,
        selectedNum: (item.fieldsItems || []).filter(field => field.isShow).length
      }))
    }
  },
  methods: {
    handleClick(tab, event) {
      this.init();
    },

    init() {
      listAssetFields({
        assetType: this.activeName
      }).then(res => {
        this.fieldData = res.rows.map((item, groupIndex) => {
          return {
            ...item,
            fieldsItems: item.fieldsItems.map((field, index) => {
              return {
                ...field,
                sort: index,
                groupIndex: groupIndex
              }
            })
          }
        })
        this.originalFieldData = JSON.parse(JSON.stringify(res.rows));
      })
    },

    // 将修改同步到原始数据
    syncFieldToSource(field) {
      const {groupIndex, fieldIndex} = field;
      if (
        this.fieldData[groupIndex] &&
        this.fieldData[groupIndex].fieldsItems[fieldIndex]
      ) {
        // 更新原始数据中的字段
        this.$set(
          this.fieldData[groupIndex].fieldsItems,
          fieldIndex,
          {...field}
        );
      }
    },

    // 编辑字段
    handleEdit(item, groupIndex, fieldIndex) {
      this.editDisabled = false;
      // 保存编辑位置信息
      this.currentField = {
        ...item,
        groupIndex,
        fieldIndex
      };
    },

    // 处理显示/隐藏状态变化
    handleVisibilityChange(field) {
      // 如果当前编辑的是这个字段，同步更新编辑表单
      if (this.currentField && this.currentField.id === field.id) {
        this.currentField.isShow = field.isShow;
      }
    },

    handleSave() {
      updateAssetFields(this.fieldData).then(res => {
        if (res.code === 200) this.$message.success('保存成功')
        this.editDisabled = true;
        this.currentField = {};
        this.init()
      })
    },

    // 拖拽事件处理
    onChange(event) {
      const { moved } = event;
      if (moved) {
        // 获取移动的元素所在的组
        const groupIndex = moved.element.groupIndex;
        const fieldsItems = this.fieldData[groupIndex].fieldsItems;

        // 更新每个字段的 sort 字段
        fieldsItems.forEach((field, index) => {
          field.sort = index;
        });
      }
    },

    handleCancel() {
      this.$confirm('确定放弃所有修改吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 恢复原始数据
        this.fieldData = JSON.parse(JSON.stringify(this.originalFieldData));
        this.editDisabled = true;
        this.currentField = {};
        this.$message.info('已恢复原始配置');
      }).catch(() => {
        this.$message.info('已取消操作');
      });
    }
  },
}
</script>

<style scoped lang="scss">
.asset-field-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .asset-field-title {
    width: 100%;
    height: 54px;
    padding: 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #ffffff;

    ::v-deep .el-tabs__header {
      margin: 0 !important;
      height: 54px;
      line-height: 54px;
    }

    ::v-deep .el-tabs__nav-wrap {
      height: 54px;
    }

    ::v-deep .el-tabs__item {
      height: 54px;
      line-height: 54px;
    }

    .btn {
      display: flex;
    }
  }

  .asset-field-content {
    width: 100%;
    height: calc(100% - 54px);
    display: flex;
    gap: 8px;

    .left-board {
      width: 300px;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      gap: 8px;

      .field-packet {
        /*height: 100%;*/
        padding: 0 12px;
        background: #fff;
      }
    }

    /*.center-board {
      flex: 1;
      background: #fff;
    }*/

    .right-board {
     /* width: 300px;*/
      flex: 1;
      height: 100%;
      background: #fff;
    }

    .board-title {
      height: 50px;
      line-height: 50px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid rgba(206, 206, 206, 1);

      .title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: rgba(51, 51, 51, 1);

        img {
          width: 25px;
          height: 25px;
          margin-right: 5px;
        }
      }
    }

    .board-content {
      .field-item {
        height: 25px;
        line-height: 25px;
        margin: 12px 0;
        display: flex;
        align-items: center;

        div {
          flex: 1;
          margin: 0 12px 0 6px;
          text-indent: 12px;
          background: #E8F4FF;
        }
      }
    }
  }
}
</style>
